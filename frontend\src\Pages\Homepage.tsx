import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence, useSpring } from "framer-motion";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import {
  Upload,
  FileText,
  Settings2,
  Image as ImageIcon,
  Loader2,
  Copy,
  Check,
  X,
  ArrowRight,
  Globe,
  ExternalLink,
  Layout,
  AlertCircle,
  Sparkles,
} from "lucide-react";

import "./Homepage.css";

// Form schema validation
const formSchema = z.object({
  imageFile: z.instanceof(File).nullable(),
  ocrType: z.enum(["ocr", "format"]),
  useCrop: z.boolean(),
  renderOutput: z.boolean(),
  openBrowser: z.boolean(),
  ocrBox: z.string().optional(),
  ocrColor: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

const dummyImages = [
  "/sample-images/receipt.jpg",
  "/sample-images/document.jpg",
  "/sample-images/business-card.jpg",
];

export default function Homepage() {
  // States
  const [file, setFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<string>(
    "Upload an image to start extraction"
  );
  const [htmlContent, setHtmlContent] = useState<string>("");
  const [showRendered, setShowRendered] = useState<boolean>(false);
  const [viewLink, setViewLink] = useState<string>("");
  const [copied, setCopied] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const uploadRef = useRef<HTMLDivElement>(null);
  const resultRef = useRef<HTMLDivElement>(null);

  // Form setup
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      imageFile: null,
      ocrType: "ocr",
      useCrop: false,
      renderOutput: false,
      openBrowser: false,
      ocrBox: "",
      ocrColor: "",
    },
  });

  const ocrType = watch("ocrType");
  const useCrop = watch("useCrop");
  const renderOutput = watch("renderOutput");
  const openBrowser = watch("openBrowser");

  // Add new animation configs
  const uploadAreaVariants = {
    idle: { scale: 1 },
    hover: {
      scale: 1.02,
      boxShadow: "0 0 30px rgba(99, 102, 241, 0.2)",
      transition: { type: "spring", stiffness: 400, damping: 10 },
    },
  };

  const springConfig = { stiffness: 100, damping: 10 };
  const x = useSpring(0, springConfig);
  const y = useSpring(0, springConfig);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const rect = uploadRef.current?.getBoundingClientRect();
      if (rect) {
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        const moveX = (e.clientX - centerX) * 0.1;
        const moveY = (e.clientY - centerY) * 0.1;
        x.set(moveX);
        y.set(moveY);
      }
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, [x, y]);

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      processFile(selectedFile);
    }
  };

  // Process the selected file
  const processFile = (selectedFile: File) => {
    setFile(selectedFile);
    setValue("imageFile", selectedFile);

    // Create preview
    const reader = new FileReader();
    reader.onload = (event) => {
      setFilePreview(event.target?.result as string);
      toast.success("Image uploaded successfully");
    };
    reader.readAsDataURL(selectedFile);
  };

  // Drag handlers
  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      processFile(e.dataTransfer.files[0]);
    }
  };

  // Copy to clipboard function
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(result);
      setCopied(true);
      toast.success("Text copied to clipboard");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy text");
      console.error("Failed to copy text: ", err);
    }
  };

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    if (!file) {
      toast.error("Please select an image first");
      return;
    }

    setIsLoading(true);
    setResult("Processing your image...");
    setShowRendered(false);

    if (resultRef.current) {
      resultRef.current.scrollIntoView({ behavior: "smooth" });
    }

    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("ocr_type", data.ocrType);
      formData.append("use_crop", String(data.useCrop));
      formData.append("render", String(data.renderOutput));
      formData.append("open_browser", String(data.openBrowser));

      // Add advanced options if provided
      if (data.ocrBox) formData.append("ocr_box", data.ocrBox);
      if (data.ocrColor) formData.append("ocr_color", data.ocrColor);

      const response = await fetch("http://localhost:5000/ocr", {
        method: "POST",
        body: formData,
      });

      const responseData = await response.json();

      if (response.ok) {
        setResult(responseData.text || "No text detected");
        toast.success("Image processed successfully");

        if (responseData.html) {
          setShowRendered(true);
          setHtmlContent(responseData.html);

          if (responseData.local_file) {
            setViewLink(`file:///${responseData.local_file}`);
          } else if (responseData.render_url) {
            setViewLink(responseData.render_url);
          }
        } else {
          setShowRendered(false);
        }
      } else {
        setResult(`Error: ${responseData.error || "Unknown error"}`);
        toast.error("Error processing image");
      }
    } catch (error) {
      setResult(`Network error: ${(error as Error).message}`);
      toast.error("Network error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  // Clear the form
  const handleClear = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    setFile(null);
    setFilePreview(null);
    setValue("imageFile", null);
    setValue("ocrType", "ocr");
    setValue("useCrop", false);
    setValue("renderOutput", false);
    setValue("openBrowser", false);
    setValue("ocrBox", "");
    setValue("ocrColor", "");
    setResult("Upload an image to start extraction");
    setShowRendered(false);
    setHtmlContent("");
    setViewLink("");
    toast.info("Form cleared");
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Floating Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-[40%] -right-[40%] w-[80%] h-[80%] rounded-full bg-indigo-100/30 blur-3xl" />
        <div className="absolute -bottom-[40%] -left-[40%] w-[80%] h-[80%] rounded-full bg-purple-100/30 blur-3xl" />
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]" />
      </div>

      <div className="relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Enhanced Header */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-indigo-50 rounded-full mb-6">
              <span className="flex h-2 w-2 relative">
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-indigo-400 opacity-75"></span>
                <span className="relative inline-flex rounded-full h-2 w-2 bg-indigo-500"></span>
              </span>
              <span className="text-sm font-medium text-indigo-700">
                AI-Powered OCR Engine
              </span>
            </div>

            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl lg:text-6xl mb-6">
              Extract Text with
              <span className="bg-gradient-to-r from-indigo-600 to-violet-600 bg-clip-text text-transparent px-3">
                AI Precision
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Upload your image and watch our advanced AI extract text with
              unparalleled accuracy
            </p>
          </div>

          {/* Quick Start Guide */}
          <div className="mb-12">
            <div className="flex overflow-x-auto pb-4 gap-4 snap-x">
              {dummyImages.map((img, index) => (
                <div
                  key={index}
                  className="flex-none snap-center w-[300px] group cursor-pointer hover:scale-105 transition-transform duration-200"
                  onClick={() => {
                    // Implement quick start with sample image
                  }}
                >
                  <div className="relative rounded-xl overflow-hidden border-2 border-gray-200 hover:border-indigo-300 transition-colors">
                    <img
                      src={img}
                      alt="Sample document"
                      className="w-full h-[200px] object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end p-4">
                      <span className="text-white text-sm font-medium">
                        Try with sample {index + 1}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Upload Section */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden backdrop-blur-xl bg-white/50">
              <div className="p-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-2xl font-semibold text-gray-900 flex items-center gap-2">
                    <ImageIcon className="h-6 w-6 text-indigo-600" />
                    Image Upload
                  </h2>
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="px-3 py-1 text-sm font-medium text-indigo-600 bg-indigo-50 rounded-full border border-indigo-100"
                  >
                    Step 1
                  </motion.div>
                </div>

                <motion.div
                  ref={uploadRef}
                  style={{ x, y }}
                  className={`
                    relative border-2 border-dashed rounded-xl min-h-[300px]
                    ${
                      dragActive
                        ? "border-indigo-400 bg-indigo-50/50"
                        : "border-gray-200"
                    } 
                    ${errors.imageFile ? "border-red-300 bg-red-50" : ""}
                    hover:border-indigo-300 transition-all duration-200
                    group cursor-pointer overflow-hidden
                  `}
                  whileHover={{ scale: 1.005 }}
                  whileTap={{ scale: 0.995 }}
                  onClick={() => document.getElementById("imageFile")?.click()}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <input
                    type="file"
                    id="imageFile"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileChange}
                  />

                  <div className="absolute inset-0 flex items-center justify-center">
                    {filePreview ? (
                      <div className="relative w-full h-full">
                        <img
                          src={filePreview}
                          alt="Preview"
                          className="w-full h-full object-contain"
                        />
                        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                          <p className="text-white text-lg font-medium">
                            Click or drag to change image
                          </p>
                        </div>
                        <button
                          onClick={handleClear}
                          className="absolute top-4 right-4 p-2 rounded-full bg-black/80 text-white hover:bg-red-500 transition-colors"
                        >
                          <X className="h-5 w-5" />
                        </button>
                        <div className="absolute bottom-4 left-4 right-4 bg-black/80 text-white text-sm py-2 px-3 rounded-lg truncate">
                          {file?.name}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center p-12">
                        <div className="relative inline-flex mb-6">
                          <Upload
                            className="h-16 w-16 text-indigo-300"
                            strokeWidth={1.5}
                          />
                          <motion.div
                            className="absolute inset-0 text-indigo-600 opacity-0"
                            animate={{ opacity: [0, 1, 0] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          >
                            <Upload className="h-16 w-16" strokeWidth={1.5} />
                          </motion.div>
                        </div>
                        <h3 className="text-xl font-medium text-gray-900 mb-2">
                          {dragActive
                            ? "Drop your image here"
                            : "Drag and drop your image"}
                        </h3>
                        <p className="text-gray-500">
                          or click to select from your computer
                        </p>
                        <div className="mt-4 flex flex-wrap justify-center gap-2 text-xs text-gray-500">
                          <span className="px-2 py-1 bg-gray-100 rounded">
                            PNG
                          </span>
                          <span className="px-2 py-1 bg-gray-100 rounded">
                            JPG
                          </span>
                          <span className="px-2 py-1 bg-gray-100 rounded">
                            PDF
                          </span>
                          <span className="px-2 py-1 bg-gray-100 rounded">
                            Max 10MB
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>

                {/* Enhanced OCR Settings */}
                <div className="mt-8">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                      <Settings2 className="h-5 w-5 text-indigo-600" />
                      Processing Options
                    </h3>
                    <span className="text-sm text-gray-500">
                      Customize extraction
                    </span>
                  </div>

                  <div className="space-y-6">
                    {/* OCR Type Selector */}
                    <div className="p-4 rounded-xl bg-gray-50 border border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="p-2 rounded-lg bg-white shadow-sm border border-gray-200">
                            <FileText className="h-5 w-5 text-indigo-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">
                              OCR Type
                            </p>
                            <p className="text-sm text-gray-500">
                              Choose extraction method
                            </p>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <button
                            type="button"
                            onClick={() => setValue("ocrType", "ocr")}
                            className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                              ocrType === "ocr"
                                ? "bg-indigo-600 text-white shadow-lg shadow-indigo-200"
                                : "bg-white text-gray-700 border border-gray-200 hover:border-indigo-300"
                            }`}
                          >
                            Basic
                          </button>
                          <button
                            type="button"
                            onClick={() => setValue("ocrType", "format")}
                            className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                              ocrType === "format"
                                ? "bg-indigo-600 text-white shadow-lg shadow-indigo-200"
                                : "bg-white text-gray-700 border border-gray-200 hover:border-indigo-300"
                            }`}
                          >
                            Formatted
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Options Grid */}
                    <div className="grid sm:grid-cols-2 gap-4">
                      {[
                        {
                          id: "useCrop",
                          label: "Smart Cropping",
                          description: "Auto-detect text regions",
                          icon: <Layout className="h-5 w-5" />,
                        },
                        {
                          id: "renderOutput",
                          label: "Rich Preview",
                          description: "See formatted results",
                          icon: <Sparkles className="h-5 w-5" />,
                        },
                        {
                          id: "openBrowser",
                          label: "Quick View",
                          description: "Open results in browser",
                          icon: <Globe className="h-5 w-5" />,
                        },
                      ].map((option) => (
                        <label
                          key={option.id}
                          className="group relative flex items-start p-4 rounded-xl bg-gray-50 border border-gray-200 cursor-pointer hover:border-indigo-300 transition-colors"
                        >
                          <div className="flex items-center h-5">
                            <input
                              type="checkbox"
                              {...register(option.id as keyof FormValues)}
                              className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                            />
                          </div>
                          <div className="ml-3 flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-white shadow-sm border border-gray-200 group-hover:border-indigo-300 transition-colors">
                              {option.icon}
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">
                                {option.label}
                              </p>
                              <p className="text-sm text-gray-500">
                                {option.description}
                              </p>
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>

                    {/* Process Button */}
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleSubmit(onSubmit)}
                      disabled={!file || isLoading}
                      className="w-full flex items-center justify-center gap-2 px-6 py-4 rounded-xl bg-gradient-to-r from-indigo-600 to-violet-600 text-white hover:from-indigo-700 hover:to-violet-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg shadow-indigo-200/50"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="h-5 w-5 animate-spin" />
                          Processing Image...
                        </>
                      ) : (
                        <>
                          Extract Text
                          <ArrowRight className="h-5 w-5" />
                        </>
                      )}
                    </motion.button>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Results Section */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden backdrop-blur-xl bg-white/50">
              <div className="p-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-2xl font-semibold text-gray-900 flex items-center gap-2">
                    <FileText className="h-6 w-6 text-indigo-600" />
                    Extracted Text
                  </h2>
                  {result !== "Upload an image to start extraction" && (
                    <button
                      onClick={copyToClipboard}
                      className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-indigo-700 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors"
                    >
                      {copied ? (
                        <>
                          <Check className="h-4 w-4 text-green-500" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4" />
                          Copy Text
                        </>
                      )}
                    </button>
                  )}
                </div>

                <div className="relative min-h-[500px]">
                  {isLoading ? (
                    <div className="absolute inset-0 flex flex-col items-center justify-center">
                      <div className="relative">
                        <div className="w-20 h-20 border-4 border-indigo-100 rounded-full animate-pulse"></div>
                        <div className="absolute top-0 left-0 w-20 h-20 border-4 border-indigo-600 rounded-full animate-spin border-t-transparent"></div>
                      </div>
                      <div className="mt-6 space-y-2 text-center">
                        <p className="text-lg font-medium text-gray-900">
                          Processing your image
                        </p>
                        <p className="text-gray-500">
                          Using AI to extract text with high accuracy
                        </p>
                      </div>
                    </div>
                  ) : result === "Upload an image to start extraction" ? (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center space-y-4">
                        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-50 text-indigo-600 mb-4">
                          <AlertCircle className="h-8 w-8" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">
                          No Text Extracted Yet
                        </h3>
                        <p className="text-gray-500">
                          Upload an image to start the extraction process
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-6 bg-red-800 h-screen ">
                      <div className="rounded-xl border border-gray-200 overflow-hidden bg-gray-50 h-full">
                        <div className="flex items-center justify-between px-4 py-2 bg-gray-100 border-b border-gray-200">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-red-400"></div>
                            <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                            <div className="w-3 h-3 rounded-full bg-green-400"></div>
                          </div>
                          <span className="text-xs text-gray-500 font-mono">
                            extracted_text.txt
                          </span>
                        </div>
                        <div className="m-5">{result}</div>
                      </div>

                      {showRendered && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="space-y-4"
                        >
                          <div className="border-t border-gray-200 pt-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                              <Sparkles className="h-5 w-5 text-indigo-600" />
                              Formatted Preview
                            </h3>
                            <div className="p-6 rounded-xl bg-white border border-gray-200 prose max-w-none">
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: htmlContent,
                                }}
                              />
                            </div>
                          </div>

                          {viewLink && (
                            <a
                              href={viewLink}
                              target="_blank"
                              rel="noreferrer"
                              className="flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium text-indigo-700 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors"
                            >
                              <Globe className="h-4 w-4" />
                              Open in Browser
                              <ExternalLink className="h-4 w-4" />
                            </a>
                          )}
                        </motion.div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
