import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence, useSpring } from "framer-motion";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import {
  Upload,
  FileText,
  Image as ImageIcon,
  Loader2,
  Copy,
  Check,
  X,
  ArrowRight,
  AlertCircle,
} from "lucide-react";

import "./Homepage.css";

// Form schema validation - simplified for basic OCR only
const formSchema = z.object({
  imageFile: z.instanceof(File).nullable(),
});

type FormValues = z.infer<typeof formSchema>;

export default function Homepage() {
  // States - simplified for basic OCR
  const [file, setFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<string>(
    "Upload an image to start extraction"
  );
  const [copied, setCopied] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const uploadRef = useRef<HTMLDivElement>(null);
  const resultRef = useRef<HTMLDivElement>(null);

  // Form setup - simplified
  const {
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      imageFile: null,
    },
  });

  // Add new animation configs
  const uploadAreaVariants = {
    idle: { scale: 1 },
    hover: {
      scale: 1.02,
      boxShadow: "0 0 30px rgba(99, 102, 241, 0.2)",
      transition: { type: "spring", stiffness: 400, damping: 10 },
    },
  };

  const springConfig = { stiffness: 100, damping: 10 };
  const x = useSpring(0, springConfig);
  const y = useSpring(0, springConfig);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const rect = uploadRef.current?.getBoundingClientRect();
      if (rect) {
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        const moveX = (e.clientX - centerX) * 0.1;
        const moveY = (e.clientY - centerY) * 0.1;
        x.set(moveX);
        y.set(moveY);
      }
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, [x, y]);

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      processFile(selectedFile);
    }
  };

  // Process the selected file
  const processFile = (selectedFile: File) => {
    setFile(selectedFile);
    setValue("imageFile", selectedFile);

    // Create preview
    const reader = new FileReader();
    reader.onload = (event) => {
      setFilePreview(event.target?.result as string);
      toast.success("Image uploaded successfully");
    };
    reader.readAsDataURL(selectedFile);
  };

  // Drag handlers
  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      processFile(e.dataTransfer.files[0]);
    }
  };

  // Copy to clipboard function
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(result);
      setCopied(true);
      toast.success("Text copied to clipboard");
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error("Failed to copy text");
      console.error("Failed to copy text: ", err);
    }
  };

  // Handle form submission - simplified for basic OCR
  const onSubmit = async (data: FormValues) => {
    if (!file) {
      toast.error("Please select an image first");
      return;
    }

    setIsLoading(true);
    setResult("Processing your image...");

    if (resultRef.current) {
      resultRef.current.scrollIntoView({ behavior: "smooth" });
    }

    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("http://localhost:5000/ocr", {
        method: "POST",
        body: formData,
      });

      const responseData = await response.json();

      if (response.ok) {
        setResult(responseData.text || "No text detected");
        toast.success("Image processed successfully");
      } else {
        setResult(`Error: ${responseData.error || "Unknown error"}`);
        toast.error("Error processing image");
      }
    } catch (error) {
      setResult(`Network error: ${(error as Error).message}`);
      toast.error("Network error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  // Clear the form - simplified
  const handleClear = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    setFile(null);
    setFilePreview(null);
    setValue("imageFile", null);
    setResult("Upload an image to start extraction");
    toast.info("Form cleared");
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Floating Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-[40%] -right-[40%] w-[80%] h-[80%] rounded-full bg-indigo-100/30 blur-3xl" />
        <div className="absolute -bottom-[40%] -left-[40%] w-[80%] h-[80%] rounded-full bg-purple-100/30 blur-3xl" />
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]" />
      </div>

      <div className="relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Enhanced Header */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-indigo-50 rounded-full mb-6">
              <span className="flex h-2 w-2 relative">
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-indigo-400 opacity-75"></span>
                <span className="relative inline-flex rounded-full h-2 w-2 bg-indigo-500"></span>
              </span>
              <span className="text-sm font-medium text-indigo-700">
                Basic OCR Service
              </span>
            </div>

            <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl lg:text-6xl mb-6">
              Simple Text
              <span className="bg-gradient-to-r from-indigo-600 to-violet-600 bg-clip-text text-transparent px-3">
                Extraction
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Upload your image and extract text quickly and easily
            </p>
          </div>

          {/* Main Content */}
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Upload Section */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden backdrop-blur-xl bg-white/50">
              <div className="p-8">
                <div className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-900 flex items-center gap-2">
                    <ImageIcon className="h-6 w-6 text-indigo-600" />
                    Upload Image
                  </h2>
                  <p className="text-gray-600 mt-2">
                    Select an image to extract text from
                  </p>
                </div>

                <motion.div
                  ref={uploadRef}
                  style={{ x, y }}
                  className={`
                    relative border-2 border-dashed rounded-xl min-h-[300px]
                    ${
                      dragActive
                        ? "border-indigo-400 bg-indigo-50/50"
                        : "border-gray-200"
                    }
                    ${errors.imageFile ? "border-red-300 bg-red-50" : ""}
                    hover:border-indigo-300 transition-all duration-200
                    group cursor-pointer overflow-hidden
                  `}
                  whileHover={{ scale: 1.005 }}
                  whileTap={{ scale: 0.995 }}
                  onClick={() => document.getElementById("imageFile")?.click()}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <input
                    type="file"
                    id="imageFile"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileChange}
                  />

                  <div className="absolute inset-0 flex items-center justify-center">
                    {filePreview ? (
                      <div className="relative w-full h-full">
                        <img
                          src={filePreview}
                          alt="Preview"
                          className="w-full h-full object-contain"
                        />
                        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                          <p className="text-white text-lg font-medium">
                            Click or drag to change image
                          </p>
                        </div>
                        <button
                          onClick={handleClear}
                          className="absolute top-4 right-4 p-2 rounded-full bg-black/80 text-white hover:bg-red-500 transition-colors"
                        >
                          <X className="h-5 w-5" />
                        </button>
                        <div className="absolute bottom-4 left-4 right-4 bg-black/80 text-white text-sm py-2 px-3 rounded-lg truncate">
                          {file?.name}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center p-12">
                        <div className="relative inline-flex mb-6">
                          <Upload
                            className="h-16 w-16 text-indigo-300"
                            strokeWidth={1.5}
                          />
                          <motion.div
                            className="absolute inset-0 text-indigo-600 opacity-0"
                            animate={{ opacity: [0, 1, 0] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          >
                            <Upload className="h-16 w-16" strokeWidth={1.5} />
                          </motion.div>
                        </div>
                        <h3 className="text-xl font-medium text-gray-900 mb-2">
                          {dragActive
                            ? "Drop your image here"
                            : "Drag and drop your image"}
                        </h3>
                        <p className="text-gray-500">
                          or click to select from your computer
                        </p>
                        <div className="mt-4 flex flex-wrap justify-center gap-2 text-xs text-gray-500">
                          <span className="px-2 py-1 bg-gray-100 rounded">
                            PNG
                          </span>
                          <span className="px-2 py-1 bg-gray-100 rounded">
                            JPG
                          </span>
                          <span className="px-2 py-1 bg-gray-100 rounded">
                            JPEG
                          </span>
                          <span className="px-2 py-1 bg-gray-100 rounded">
                            GIF
                          </span>
                          <span className="px-2 py-1 bg-gray-100 rounded">
                            TIFF
                          </span>
                          <span className="px-2 py-1 bg-gray-100 rounded">
                            Max 16MB
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>

                {/* Simple Process Button */}
                <div className="mt-8">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleSubmit(onSubmit)}
                    disabled={!file || isLoading}
                    className="w-full flex items-center justify-center gap-2 px-6 py-4 rounded-xl bg-gradient-to-r from-indigo-600 to-violet-600 text-white hover:from-indigo-700 hover:to-violet-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg shadow-indigo-200/50"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-5 w-5 animate-spin" />
                        Processing Image...
                      </>
                    ) : (
                      <>
                        Extract Text
                        <ArrowRight className="h-5 w-5" />
                      </>
                    )}
                  </motion.button>
                </div>
              </div>
            </div>

            {/* Results Section */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden backdrop-blur-xl bg-white/50">
              <div className="p-8">
                <div className="flex items-center justify-between mb-8">
                  <div>
                    <h2 className="text-2xl font-semibold text-gray-900 flex items-center gap-2">
                      <FileText className="h-6 w-6 text-indigo-600" />
                      Extracted Text
                    </h2>
                    <p className="text-gray-600 mt-2">
                      Text extracted from your image
                    </p>
                  </div>
                  {result !== "Upload an image to start extraction" && (
                    <button
                      onClick={copyToClipboard}
                      className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-indigo-700 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition-colors"
                    >
                      {copied ? (
                        <>
                          <Check className="h-4 w-4 text-green-500" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4" />
                          Copy Text
                        </>
                      )}
                    </button>
                  )}
                </div>

                <div className="relative min-h-[500px]">
                  {isLoading ? (
                    <div className="absolute inset-0 flex flex-col items-center justify-center">
                      <div className="relative">
                        <div className="w-20 h-20 border-4 border-indigo-100 rounded-full animate-pulse"></div>
                        <div className="absolute top-0 left-0 w-20 h-20 border-4 border-indigo-600 rounded-full animate-spin border-t-transparent"></div>
                      </div>
                      <div className="mt-6 space-y-2 text-center">
                        <p className="text-lg font-medium text-gray-900">
                          Processing your image
                        </p>
                        <p className="text-gray-500">
                          Extracting text from your image
                        </p>
                      </div>
                    </div>
                  ) : result === "Upload an image to start extraction" ? (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center space-y-4">
                        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-50 text-indigo-600 mb-4">
                          <AlertCircle className="h-8 w-8" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900">
                          No Text Extracted Yet
                        </h3>
                        <p className="text-gray-500">
                          Upload an image to start the extraction process
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="rounded-xl border border-gray-200 overflow-hidden bg-gray-50">
                      <div className="flex items-center justify-between px-4 py-2 bg-gray-100 border-b border-gray-200">
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-red-400"></div>
                          <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                          <div className="w-3 h-3 rounded-full bg-green-400"></div>
                        </div>
                        <span className="text-xs text-gray-500 font-mono">
                          extracted_text.txt
                        </span>
                      </div>
                      <div className="p-6 max-h-96 overflow-y-auto">
                        <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
                          {result}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
