# OCR Model Training Guide

This guide explains how to train/fine-tune your OCR model for better performance on your specific use cases.

## Table of Contents

- [Overview](#overview)
- [Training Options](#training-options)
- [Quick Start](#quick-start)
- [Detailed Training Process](#detailed-training-process)
- [Model Management](#model-management)
- [Best Practices](#best-practices)

## Overview

You have several options for improving your OCR model:

1. **Fine-tuning** (Recommended) - Adapt the existing Qwen Vision model to your data
2. **Data Augmentation** - Improve performance without retraining
3. **Custom Training** - Train from scratch (advanced)

## Training Options

### 1. Fine-tuning the Existing Model (Recommended)

**Pros:**
- Faster training (hours vs days)
- Requires less data (100-1000 samples)
- Builds on existing knowledge
- Lower computational requirements

**Cons:**
- Limited to the base model's capabilities
- May not work for very different domains

### 2. Training from Scratch

**Pros:**
- Complete control over architecture
- Can handle any domain
- Potentially better performance

**Cons:**
- Requires large datasets (10k+ samples)
- Very long training time (days/weeks)
- High computational requirements
- Complex implementation

## Quick Start

### Step 1: Prepare Your Training Data

```bash
# Create training data structure
python prepare_training_data.py --action create --data-dir ./my_training_data

# This creates:
# my_training_data/
# ├── images/          # Put your images here
# └── annotations.json # Image-text pairs
```

### Step 2: Add Your Data

1. **Add images** to `my_training_data/images/`
2. **Update annotations.json** with the format:
```json
[
  {
    "image": "document1.jpg",
    "text": "The actual text content from this image"
  },
  {
    "image": "receipt2.png", 
    "text": "Receipt text with numbers and formatting"
  }
]
```

### Step 3: Validate Your Data

```bash
python prepare_training_data.py --action validate --data-dir ./my_training_data
```

### Step 4: Start Training

```bash
python train_model.py
```

### Step 5: Use Your Fine-tuned Model

```bash
# Install the fine-tuned model
python model_manager.py install --path ./fine_tuned_ocr_model

# Set it as active
python model_manager.py set --model fine_tuned

# Test it
python model_manager.py test --test-image test.jpg
```

## Detailed Training Process

### Data Preparation

#### Required Data Format

Your training data should follow this structure:

```
training_data/
├── images/
│   ├── image1.jpg
│   ├── image2.png
│   ├── image3.tiff
│   └── ...
└── annotations.json
```

#### Annotations Format

```json
[
  {
    "image": "filename.jpg",
    "text": "Exact text content from the image"
  }
]
```

#### Data Quality Guidelines

1. **Image Quality:**
   - Clear, readable text
   - Good contrast
   - Minimal blur or distortion
   - Various lighting conditions

2. **Text Accuracy:**
   - Exact transcription (including punctuation)
   - Preserve formatting when important
   - Include special characters if relevant

3. **Dataset Diversity:**
   - Different fonts and sizes
   - Various document types
   - Multiple languages (if needed)
   - Different image qualities

#### Recommended Dataset Sizes

- **Minimum:** 100 image-text pairs
- **Good:** 500-1000 pairs
- **Excellent:** 2000+ pairs

### Training Configuration

#### Basic Training (Recommended)

```python
# In train_model.py, modify these parameters:
trainer.train(
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    num_epochs=3,        # Start with 3, increase if needed
    batch_size=2,        # Adjust based on GPU memory
    learning_rate=5e-5   # Conservative learning rate
)
```

#### Advanced Training

For better results, you can adjust:

```python
# More epochs for complex datasets
num_epochs=5

# Larger batch size if you have more GPU memory
batch_size=4

# Learning rate scheduling
learning_rate=2e-5  # Lower for fine-tuning
```

### Training Monitoring

The training script will output:
- Loss values (should decrease over time)
- Evaluation metrics
- Training progress
- Model checkpoints

Look for:
- **Decreasing loss** - Model is learning
- **Stable evaluation loss** - Model is not overfitting
- **Reasonable training time** - Should complete in hours, not days

## Model Management

### Available Commands

```bash
# List all models
python model_manager.py list

# Set active model
python model_manager.py set --model fine_tuned

# Test a model
python model_manager.py test --model fine_tuned --test-image my_test.jpg

# Compare models
python model_manager.py compare --test-image my_test.jpg

# Backup original model
python model_manager.py backup
```

### Model Switching

You can easily switch between models:

1. **Original model** - The pre-trained Qwen Vision model
2. **Fine-tuned model** - Your custom-trained model
3. **Multiple fine-tuned models** - Different versions for different use cases

## Best Practices

### Data Collection

1. **Collect diverse data** - Different document types, fonts, qualities
2. **Quality over quantity** - 500 high-quality samples > 2000 poor samples
3. **Representative data** - Match your actual use cases
4. **Balanced dataset** - Include various text lengths and complexities

### Training Tips

1. **Start small** - Begin with a small dataset to test the pipeline
2. **Monitor overfitting** - Stop if evaluation loss starts increasing
3. **Save checkpoints** - Training can be interrupted and resumed
4. **Test frequently** - Evaluate on real examples during training

### Performance Optimization

1. **GPU usage** - Use CUDA if available for faster training
2. **Batch size** - Increase if you have more GPU memory
3. **Mixed precision** - Enabled by default for faster training
4. **Data loading** - Use multiple workers for faster data loading

### Troubleshooting

#### Common Issues

1. **Out of memory errors:**
   - Reduce batch size
   - Use smaller images
   - Enable gradient checkpointing

2. **Poor performance:**
   - Check data quality
   - Increase training data
   - Adjust learning rate

3. **Training too slow:**
   - Use GPU if available
   - Increase batch size
   - Reduce image resolution

4. **Model not improving:**
   - Check learning rate (try lower values)
   - Verify data quality
   - Increase training epochs

## Example Workflows

### Workflow 1: Document OCR Improvement

```bash
# 1. Collect 500 document images with text
# 2. Create annotations
python prepare_training_data.py --action generate --data-dir ./documents_data

# 3. Edit annotations.json with correct text
# 4. Validate data
python prepare_training_data.py --action validate --data-dir ./documents_data

# 5. Train model
python train_model.py

# 6. Install and test
python model_manager.py install --path ./fine_tuned_ocr_model
python model_manager.py set --model fine_tuned
python model_manager.py test --test-image sample_document.jpg
```

### Workflow 2: Receipt OCR Specialization

```bash
# 1. Collect receipt images
# 2. Focus on preserving formatting and numbers
# 3. Train with more epochs for better number recognition
# 4. Test with various receipt types
```

## Advanced Topics

### Custom Loss Functions

For specialized use cases, you can modify the training script to use custom loss functions that focus on specific aspects like:
- Number accuracy
- Formatting preservation
- Specific character recognition

### Multi-task Learning

Train the model on multiple related tasks:
- OCR + layout detection
- OCR + language identification
- OCR + confidence scoring

### Continuous Learning

Set up a pipeline to continuously improve your model:
1. Collect new data from production
2. Periodically retrain
3. A/B test new models
4. Deploy best performing models

## Getting Help

If you encounter issues:

1. Check the console output for error messages
2. Validate your training data format
3. Start with a small dataset to test the pipeline
4. Monitor GPU memory usage
5. Check the training logs for convergence

For specific errors, the training script provides detailed error messages and suggestions for fixes.
