<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Basic OCR Client</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      button {
        background-color: #4caf50;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
      }
      button:hover {
        background-color: #45a049;
      }
      .options {
        margin: 20px 0;
      }
      .options label {
        margin-right: 15px;
      }
      #result {
        white-space: pre-wrap;
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        min-height: 100px;
      }
      .rendered-content {
        margin-top: 20px;
        border: 1px solid #ddd;
        padding: 10px;
        border-radius: 4px;
      }
      .loader {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 2s linear infinite;
        display: none;
        margin: 20px auto;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <h1>Basic OCR Client</h1>

    <div class="card">
      <h2>Upload Image</h2>
      <form id="ocrForm">
        <input type="file" id="imageFile" accept="image/*" required />

        <button type="submit">Process Image</button>
      </form>

      <div class="loader" id="loader"></div>
    </div>

    <div class="card">
      <h2>OCR Result</h2>
      <pre id="result">Upload an image to see results...</pre>
    </div>

    <script>
      document
        .getElementById("ocrForm")
        .addEventListener("submit", async function (e) {
          e.preventDefault();

          const fileInput = document.getElementById("imageFile");
          if (!fileInput.files.length) {
            alert("Please select an image file");
            return;
          }

          const file = fileInput.files[0];
          const loader = document.getElementById("loader");
          const result = document.getElementById("result");

          // Show loader, clear previous results
          loader.style.display = "block";
          result.textContent = "Processing...";

          try {
            const formData = new FormData();
            formData.append("file", file);

            const response = await fetch("http://localhost:5000/ocr", {
              method: "POST",
              body: formData,
            });

            const data = await response.json();

            if (response.ok) {
              // Display text result
              result.textContent = data.text || "No text detected";
            } else {
              result.textContent = `Error: ${data.error || "Unknown error"}`;
            }
          } catch (error) {
            result.textContent = `Network error: ${error.message}`;
          } finally {
            // Hide loader
            loader.style.display = "none";
          }
        });
    </script>
  </body>
</html>
