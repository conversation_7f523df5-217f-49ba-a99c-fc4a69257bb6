<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OCR API Client</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      button {
        background-color: #4caf50;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
      }
      button:hover {
        background-color: #45a049;
      }
      .options {
        margin: 20px 0;
      }
      .options label {
        margin-right: 15px;
      }
      #result {
        white-space: pre-wrap;
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        min-height: 100px;
      }
      .rendered-content {
        margin-top: 20px;
        border: 1px solid #ddd;
        padding: 10px;
        border-radius: 4px;
      }
      .loader {
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 2s linear infinite;
        display: none;
        margin: 20px auto;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
      .advanced-options {
        margin-top: 15px;
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 4px;
        background-color: #f9f9f9;
        display: none;
      }
      .input-field {
        margin: 8px 0;
      }
      .input-field label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      .input-field input[type="text"] {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      .view-link {
        display: block;
        margin-top: 10px;
        color: #0066cc;
        text-decoration: underline;
        cursor: pointer;
      }
      .additional-buttons {
        margin-top: 15px;
      }
      .additional-buttons button {
        margin-right: 10px;
        background-color: #007bff;
      }
    </style>
  </head>
  <body>
    <h1>OCR API Client</h1>

    <div class="card">
      <h2>Upload Image</h2>
      <form id="ocrForm">
        <input type="file" id="imageFile" accept="image/*" required />

        <div class="options">
          <h3>OCR Options</h3>
          <div>
            <label>
              <input type="radio" name="ocrType" value="ocr" checked /> Basic
              OCR
            </label>
            <label>
              <input type="radio" name="ocrType" value="format" /> Formatted OCR
            </label>
          </div>

          <div>
            <label>
              <input type="checkbox" id="useCrop" /> Use Multi-crop
            </label>
          </div>

          <div>
            <label>
              <input type="checkbox" id="renderOutput" /> Render Output
            </label>
          </div>

          <div>
            <label>
              <input type="checkbox" id="openBrowser" /> Open Result in Browser
            </label>
          </div>

          <div class="advanced-options">
            <h4>Advanced Options</h4>
            <div class="input-field">
              <label for="ocrBox">OCR Box (format: x1,y1,x2,y2,...)</label>
              <input
                type="text"
                id="ocrBox"
                placeholder="Leave empty for default behavior"
              />
            </div>

            <div class="input-field">
              <label for="ocrColor">OCR Color (format: r,g,b)</label>
              <input
                type="text"
                id="ocrColor"
                placeholder="Leave empty for default behavior"
              />
            </div>
          </div>
        </div>

        <button type="submit">Process Image</button>
      </form>

      <div class="loader" id="loader"></div>
    </div>

    <div class="card">
      <h2>OCR Result</h2>
      <pre id="result">Upload an image to see results...</pre>

      <div id="renderedContent" class="rendered-content" style="display: none">
        <h3>Rendered Content</h3>
        <div id="htmlOutput"></div>
        <a id="viewLink" class="view-link" target="_blank"
          >Open in New Window</a
        >
      </div>
    </div>

    <script>
      document
        .getElementById("ocrForm")
        .addEventListener("submit", async function (e) {
          e.preventDefault();

          const fileInput = document.getElementById("imageFile");
          if (!fileInput.files.length) {
            alert("Please select an image file");
            return;
          }

          const file = fileInput.files[0];
          const ocrType = document.querySelector(
            'input[name="ocrType"]:checked'
          ).value;
          const useCrop = document.getElementById("useCrop").checked;
          const renderOutput = document.getElementById("renderOutput").checked;
          const openBrowser = document.getElementById("openBrowser").checked;
          const ocrBox = document.getElementById("ocrBox").value.trim();
          const ocrColor = document.getElementById("ocrColor").value.trim();

          const loader = document.getElementById("loader");
          const result = document.getElementById("result");
          const renderedContent = document.getElementById("renderedContent");
          const htmlOutput = document.getElementById("htmlOutput");
          const viewLink = document.getElementById("viewLink");

          // Show loader, clear previous results
          loader.style.display = "block";
          result.textContent = "Processing...";
          renderedContent.style.display = "none";

          try {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("ocr_type", ocrType);
            formData.append("use_crop", useCrop);
            formData.append("render", renderOutput);
            formData.append("open_browser", openBrowser);

            // Add advanced options if provided
            if (ocrBox) {
              formData.append("ocr_box", ocrBox);
            }

            if (ocrColor) {
              formData.append("ocr_color", ocrColor);
            }

            const response = await fetch("http://localhost:5000/ocr", {
              method: "POST",
              body: formData,
            });

            const data = await response.json();

            if (response.ok) {
              // Display text result
              result.textContent = data.text || "No text detected";

              // Handle rendered HTML if available
              if (data.html) {
                renderedContent.style.display = "block";
                htmlOutput.innerHTML = data.html;

                // Update the view link
                if (data.local_file) {
                  viewLink.href = `file:///${data.local_file}`;
                  viewLink.textContent = "Open Rendered HTML in New Window";
                } else if (data.render_url) {
                  viewLink.href = data.render_url;
                  viewLink.textContent = "Open Rendered HTML in New Window";
                }
              } else {
                renderedContent.style.display = "none";
              }
            } else {
              result.textContent = `Error: ${data.error || "Unknown error"}`;
            }
          } catch (error) {
            result.textContent = `Network error: ${error.message}`;
          } finally {
            // Hide loader
            loader.style.display = "none";
          }
        });
    </script>
  </body>
</html>
