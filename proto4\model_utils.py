import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

def load_model_and_tokenizer(model_name_or_path):
    """
    Load model and tokenizer with proper configuration to avoid attention mask
    and pad token ID warnings.
    
    Args:
        model_name_or_path (str): Path to model or model identifier from huggingface.co/models
        
    Returns:
        tuple: (model, tokenizer)
    """
    tokenizer = AutoTokenizer.from_pretrained(model_name_or_path)
    model = AutoModelForCausalLM.from_pretrained(model_name_or_path)
    
    # Set pad_token_id if it's not set
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
        model.config.pad_token_id = model.config.eos_token_id
        print(f"Set pad_token_id to eos_token_id: {model.config.eos_token_id}")
    
    return model, tokenizer

def generate_text(model, tokenizer, prompt, max_length=100, **kwargs):
    """
    Generate text using a model with proper attention mask handling.
    
    Args:
        model: The transformer model
        tokenizer: The tokenizer
        prompt (str): Input text
        max_length (int): Maximum length of generated text
        **kwargs: Additional arguments for model.generate()
        
    Returns:
        str: Generated text
    """
    # Tokenize with padding and create attention mask
    inputs = tokenizer(prompt, return_tensors="pt", padding=True)
    
    # Ensure attention_mask is present
    if 'attention_mask' not in inputs:
        # Create attention mask (1 for tokens, 0 for padding)
        attention_mask = torch.ones_like(inputs['input_ids'])
        inputs['attention_mask'] = attention_mask
    
    # Generate with the properly set attention mask
    outputs = model.generate(
        inputs['input_ids'],
        attention_mask=inputs['attention_mask'],
        max_length=max_length,
        pad_token_id=tokenizer.pad_token_id,
        **kwargs
    )
    
    # Decode the generated tokens
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return generated_text
