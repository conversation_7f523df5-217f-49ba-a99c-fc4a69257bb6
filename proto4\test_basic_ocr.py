#!/usr/bin/env python
# test_basic_ocr.py - Simple test script for basic OCR functionality

import requests
import os

def test_health():
    """Test the health endpoint"""
    try:
        response = requests.get("http://localhost:5000/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_ocr(image_path):
    """Test OCR with an image file"""
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return False
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post("http://localhost:5000/ocr", files=files)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ OCR successful!")
            print(f"📄 Extracted text: {data.get('text', 'No text')[:200]}...")
            return True
        else:
            print(f"❌ OCR failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ OCR error: {e}")
        return False

def main():
    print("🧪 Testing Basic OCR Backend")
    print("=" * 40)
    
    # Test health endpoint
    print("\n1. Testing health endpoint...")
    if not test_health():
        print("❌ Server is not running or not responding")
        print("💡 Make sure to run: python ocr_processor.py")
        return
    
    # Test OCR with available test images
    test_images = ["test.jpg", "test2.jpg", "test3.png", "test4.png"]
    
    print("\n2. Testing OCR functionality...")
    success_count = 0
    
    for image in test_images:
        if os.path.exists(image):
            print(f"\n📸 Testing with {image}...")
            if test_ocr(image):
                success_count += 1
            break  # Test with first available image
    else:
        print("❌ No test images found")
        print("💡 Add a test image (test.jpg, test2.jpg, etc.) to test OCR")
        return
    
    print(f"\n🎉 Testing complete! {success_count} test(s) passed.")

if __name__ == "__main__":
    main()
