import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import {
  ArrowRight,
  Code,
  Database,
  Globe,
  Cpu,
  Command,
  Github,
  CheckCircle2,
} from "lucide-react";

export default function Landing() {
  const features = [
    {
      icon: <Code className="h-6 w-6" />,
      title: "Simple Text Extraction",
      description:
        "Extract text from any image quickly and easily with our OCR service.",
    },
    {
      icon: <Database className="h-6 w-6" />,
      title: "Multiple Formats",
      description:
        "Support for PNG, JPG, JPEG, GIF, and TIFF image formats up to 16MB.",
    },
    {
      icon: <Globe className="h-6 w-6" />,
      title: "Easy Integration",
      description:
        "Simple REST API that can be integrated into any application or workflow.",
    },
    {
      icon: <Cpu className="h-6 w-6" />,
      title: "Fast Processing",
      description:
        "Quick text extraction with GPU acceleration when available.",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Modern header with subtle shadow */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link to="/" className="flex items-center gap-2">
              <Command className="h-8 w-8 text-indigo-600" />
              <span className="text-xl font-semibold bg-gradient-to-r from-indigo-600 to-violet-600 bg-clip-text text-transparent">
                Basic OCR
              </span>
            </Link>

            <nav className="hidden md:flex items-center gap-8">
              <a
                href="#features"
                className="text-gray-600 hover:text-indigo-600 transition-colors"
              >
                Features
              </a>
              <a
                href="#how-it-works"
                className="text-gray-600 hover:text-indigo-600 transition-colors"
              >
                How it Works
              </a>
              <Link
                to="/ocr"
                className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-indigo-600 text-white hover:bg-indigo-700 transition-colors"
              >
                Try It Now
                <ArrowRight className="h-4 w-4" />
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="pt-32 pb-16 md:pt-40 md:pb-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center lg:text-left"
            >
              <div className="inline-flex items-center mb-6 px-4 py-2 bg-indigo-50 rounded-full">
                <span className="w-2 h-2 bg-indigo-600 rounded-full animate-pulse mr-2" />
                <span className="text-sm font-medium text-indigo-700">
                  Basic OCR Service
                </span>
              </div>

              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Simple Text Extraction
                <span className="bg-gradient-to-r from-indigo-600 to-violet-600 bg-clip-text text-transparent">
                  {" "}
                  Made Easy
                </span>
              </h1>

              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto lg:mx-0">
                Extract text from images quickly and easily with our simple OCR
                service. Upload an image and get plain text results instantly.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link
                  to="/ocr"
                  className="inline-flex items-center justify-center gap-2 px-6 py-3 rounded-lg bg-indigo-600 text-white hover:bg-indigo-700 transition-colors"
                >
                  Get Started Free
                  <ArrowRight className="h-5 w-5" />
                </Link>
                <a
                  href="https://github.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center gap-2 px-6 py-3 rounded-lg border border-gray-200 text-gray-600 hover:bg-gray-50 transition-colors"
                >
                  View on GitHub
                  <Github className="h-5 w-5" />
                </a>
              </div>

              <div className="mt-12 flex items-center gap-6 justify-center lg:justify-start">
                <div className="flex -space-x-2">
                  {[1, 2, 3].map((i) => (
                    <div
                      key={i}
                      className="w-10 h-10 rounded-full border-2 border-white bg-gradient-to-br from-indigo-100 to-violet-100 flex items-center justify-center"
                    >
                      <CheckCircle2 className="h-5 w-5 text-indigo-600" />
                    </div>
                  ))}
                </div>
                <p className="text-sm text-gray-600">
                  <strong className="text-gray-900">Simple</strong> and reliable
                  OCR service
                </p>
              </div>
            </motion.div>

            {/* Demo Preview */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative lg:ml-4"
            >
              <div className="relative bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                <div className="flex items-center gap-2 px-4 py-3 border-b border-gray-100 bg-gray-50">
                  <div className="flex gap-1.5">
                    <div className="w-3 h-3 rounded-full bg-gray-300" />
                    <div className="w-3 h-3 rounded-full bg-gray-300" />
                    <div className="w-3 h-3 rounded-full bg-gray-300" />
                  </div>
                </div>
                <div className="p-6">
                  <img
                    src="/demo-preview.png"
                    alt="Neural OCR Demo"
                    className="rounded-lg shadow-lg border border-gray-200"
                  />
                </div>
              </div>
              {/* Decorative elements */}
              <div className="absolute -z-10 inset-0 bg-gradient-to-r from-indigo-100 to-violet-100 blur-2xl opacity-30 rounded-[32px]" />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Simple and Effective Features
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Everything you need for basic text extraction from images
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="p-6 rounded-2xl bg-white border border-gray-100 shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="w-12 h-12 rounded-lg bg-indigo-50 text-indigo-600 flex items-center justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              How It Works
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Three simple steps to extract text from your images
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                step: "01",
                title: "Upload Image",
                description: "Drag & drop or select your image file",
              },
              {
                step: "02",
                title: "Process",
                description: "Our OCR service extracts text from your image",
              },
              {
                step: "03",
                title: "Get Results",
                description: "Download or copy extracted text",
              },
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="relative p-6 rounded-2xl bg-gray-50 border border-gray-100"
              >
                <div className="text-5xl font-bold text-indigo-200 mb-4">
                  {item.step}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {item.title}
                </h3>
                <p className="text-gray-600">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            className="rounded-2xl bg-white p-12 text-center shadow-xl border border-gray-100"
          >
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Ready to Transform Your Text Extraction?
            </h2>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Join thousands of developers already using Neural OCR. Start
              extracting text with advanced AI today.
            </p>
            <Link
              to="/ocr"
              className="inline-flex items-center gap-2 px-8 py-4 rounded-lg bg-indigo-600 text-white hover:bg-indigo-700 transition-colors"
            >
              Get Started Now
              <ArrowRight className="h-5 w-5" />
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="col-span-2 md:col-span-1">
              <div className="flex items-center gap-2 mb-4">
                <Command className="h-6 w-6 text-indigo-600" />
                <span className="text-xl font-semibold">Neural OCR</span>
              </div>
              <p className="text-gray-600 mb-4">
                Next-generation OCR platform powered by advanced AI
              </p>
              <div className="flex gap-4">
                <a href="#" className="text-gray-400 hover:text-gray-600">
                  <Github className="h-5 w-5" />
                </a>
              </div>
            </div>

            {["Product", "Company", "Support"].map((section) => (
              <div key={section}>
                <h4 className="font-semibold text-gray-900 mb-4">{section}</h4>
                <ul className="space-y-2">
                  {["Features", "Documentation", "Blog"].map((item) => (
                    <li key={item}>
                      <a
                        href="#"
                        className="text-gray-600 hover:text-indigo-600"
                      >
                        {item}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="mt-12 pt-8 border-t border-gray-100 text-center text-gray-600">
            <p>© 2024 Neural OCR. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
