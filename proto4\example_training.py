#!/usr/bin/env python
# example_training.py - Complete example of training workflow

import os
import json
from PIL import Image, ImageDraw, ImageFont
import random

def create_sample_training_data():
    """Create sample training data for demonstration"""
    
    print("Creating sample training data...")
    
    # Create directories
    os.makedirs("sample_training_data/images", exist_ok=True)
    
    # Sample texts to generate
    sample_texts = [
        "Hello World",
        "Sample Document Text",
        "Invoice #12345",
        "Total: $99.99",
        "Date: 2024-01-15",
        "Customer Name: <PERSON>",
        "Address: 123 Main St",
        "Phone: (*************",
        "Email: <EMAIL>",
        "Thank you for your business!"
    ]
    
    annotations = []
    
    # Generate sample images with text
    for i, text in enumerate(sample_texts):
        # Create a simple image with text
        img = Image.new('RGB', (400, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            # Try to use a system font
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            # Fallback to default font
            font = ImageFont.load_default()
        
        # Add some variation
        x = random.randint(10, 50)
        y = random.randint(20, 40)
        color = random.choice(['black', 'darkblue', 'darkgreen'])
        
        draw.text((x, y), text, fill=color, font=font)
        
        # Save image
        filename = f"sample_{i+1:02d}.png"
        img_path = f"sample_training_data/images/{filename}"
        img.save(img_path)
        
        # Add to annotations
        annotations.append({
            "image": filename,
            "text": text
        })
    
    # Save annotations
    with open("sample_training_data/annotations.json", 'w') as f:
        json.dump(annotations, f, indent=2)
    
    print(f"Created {len(sample_texts)} sample images and annotations")
    print("Sample data saved to: sample_training_data/")

def run_complete_training_example():
    """Run a complete training example from start to finish"""
    
    print("=" * 60)
    print("COMPLETE OCR MODEL TRAINING EXAMPLE")
    print("=" * 60)
    
    # Step 1: Create sample data
    print("\n1. Creating sample training data...")
    create_sample_training_data()
    
    # Step 2: Validate data
    print("\n2. Validating training data...")
    os.system("python prepare_training_data.py --action validate --data-dir sample_training_data")
    
    # Step 3: Show training command
    print("\n3. Training command (run this manually):")
    print("   python train_model.py")
    print("   Note: This will take time and requires the model to be properly loaded")
    
    # Step 4: Show model management commands
    print("\n4. After training, manage your models:")
    print("   python model_manager.py install --path ./fine_tuned_ocr_model")
    print("   python model_manager.py set --model fine_tuned")
    print("   python model_manager.py test --test-image sample_training_data/images/sample_01.png")
    
    print("\n5. Compare models:")
    print("   python model_manager.py compare --test-image sample_training_data/images/sample_01.png")
    
    print("\n" + "=" * 60)
    print("TRAINING WORKFLOW COMPLETE!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Replace sample data with your real training images")
    print("2. Update annotations.json with correct text")
    print("3. Run the training command")
    print("4. Test and compare your models")

def create_real_world_example():
    """Create a more realistic training data example"""
    
    print("Creating realistic training data structure...")
    
    # Create structure for different document types
    doc_types = ["receipts", "invoices", "forms", "handwritten"]
    
    for doc_type in doc_types:
        os.makedirs(f"real_training_data/{doc_type}/images", exist_ok=True)
        
        # Create sample annotations for each type
        if doc_type == "receipts":
            sample_annotations = [
                {
                    "image": "receipt_001.jpg",
                    "text": "GROCERY STORE\n123 Main St\nTotal: $45.67\nDate: 01/15/2024"
                },
                {
                    "image": "receipt_002.jpg", 
                    "text": "RESTAURANT\nBurger: $12.99\nFries: $3.99\nTotal: $16.98"
                }
            ]
        elif doc_type == "invoices":
            sample_annotations = [
                {
                    "image": "invoice_001.pdf",
                    "text": "INVOICE #INV-2024-001\nBill To: ABC Company\nAmount Due: $1,250.00"
                }
            ]
        elif doc_type == "forms":
            sample_annotations = [
                {
                    "image": "form_001.png",
                    "text": "Name: John Smith\nAddress: 456 Oak Ave\nPhone: (*************"
                }
            ]
        else:  # handwritten
            sample_annotations = [
                {
                    "image": "handwritten_001.jpg",
                    "text": "Meeting notes:\n- Review project status\n- Plan next steps"
                }
            ]
        
        # Save annotations for this document type
        with open(f"real_training_data/{doc_type}/annotations.json", 'w') as f:
            json.dump(sample_annotations, f, indent=2)
    
    # Create master annotations combining all types
    all_annotations = []
    for doc_type in doc_types:
        with open(f"real_training_data/{doc_type}/annotations.json", 'r') as f:
            type_annotations = json.load(f)
            for ann in type_annotations:
                ann["image"] = f"{doc_type}/images/{ann['image']}"
            all_annotations.extend(type_annotations)
    
    # Save master annotations
    with open("real_training_data/annotations.json", 'w') as f:
        json.dump(all_annotations, f, indent=2)
    
    print("Realistic training structure created:")
    print("real_training_data/")
    print("├── receipts/images/")
    print("├── invoices/images/")
    print("├── forms/images/")
    print("├── handwritten/images/")
    print("└── annotations.json")
    print("\nAdd your real images to the appropriate folders!")

def show_training_tips():
    """Show practical training tips"""
    
    print("\n" + "=" * 60)
    print("PRACTICAL TRAINING TIPS")
    print("=" * 60)
    
    tips = [
        "Start with 100-200 high-quality image-text pairs",
        "Ensure text transcriptions are 100% accurate",
        "Include variety: different fonts, sizes, layouts",
        "Test on real examples throughout training",
        "Monitor GPU memory usage during training",
        "Save checkpoints frequently",
        "Compare before/after performance",
        "Use validation set to prevent overfitting"
    ]
    
    for i, tip in enumerate(tips, 1):
        print(f"{i}. {tip}")
    
    print("\n" + "=" * 60)
    print("COMMON ISSUES & SOLUTIONS")
    print("=" * 60)
    
    issues = [
        ("Out of memory", "Reduce batch_size in train_model.py"),
        ("Training too slow", "Use GPU, increase batch_size"),
        ("Poor results", "Check data quality, increase dataset size"),
        ("Model not improving", "Lower learning rate, more epochs"),
        ("Overfitting", "Add more diverse data, early stopping")
    ]
    
    for issue, solution in issues:
        print(f"❌ {issue}")
        print(f"✅ {solution}\n")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="OCR Training Examples")
    parser.add_argument("--action", 
                       choices=["sample", "complete", "realistic", "tips"], 
                       default="complete",
                       help="What example to run")
    
    args = parser.parse_args()
    
    if args.action == "sample":
        create_sample_training_data()
    elif args.action == "complete":
        run_complete_training_example()
    elif args.action == "realistic":
        create_real_world_example()
    elif args.action == "tips":
        show_training_tips()

if __name__ == "__main__":
    main()
