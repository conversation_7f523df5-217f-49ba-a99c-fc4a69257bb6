#!/usr/bin/env python
# model_manager.py - Manage different OCR models (original vs fine-tuned)

import os
import torch
import shutil
from transformers import AutoModel, AutoTokenizer
import json
from datetime import datetime

class ModelManager:
    """Manage OCR models and switch between them"""
    
    def __init__(self, models_dir: str = "./models"):
        self.models_dir = models_dir
        self.config_file = os.path.join(models_dir, "model_config.json")
        self.ensure_models_dir()
        
    def ensure_models_dir(self):
        """Create models directory structure"""
        os.makedirs(self.models_dir, exist_ok=True)
        
        # Create subdirectories
        os.makedirs(os.path.join(self.models_dir, "original"), exist_ok=True)
        os.makedirs(os.path.join(self.models_dir, "fine_tuned"), exist_ok=True)
        
    def get_config(self) -> dict:
        """Get current model configuration"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r') as f:
                return json.load(f)
        else:
            # Default configuration
            return {
                "active_model": "original",
                "models": {
                    "original": {
                        "path": "./model/snapshots/979938bf89ccdc949c0131ddd3841e24578a4742",
                        "description": "Original Qwen Vision model",
                        "created": datetime.now().isoformat()
                    }
                }
            }
    
    def save_config(self, config: dict):
        """Save model configuration"""
        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=2)
    
    def register_model(self, model_name: str, model_path: str, description: str = ""):
        """Register a new model"""
        config = self.get_config()
        
        config["models"][model_name] = {
            "path": model_path,
            "description": description,
            "created": datetime.now().isoformat()
        }
        
        self.save_config(config)
        print(f"Registered model '{model_name}' at {model_path}")
    
    def set_active_model(self, model_name: str):
        """Set the active model"""
        config = self.get_config()
        
        if model_name not in config["models"]:
            raise ValueError(f"Model '{model_name}' not found. Available models: {list(config['models'].keys())}")
        
        config["active_model"] = model_name
        self.save_config(config)
        print(f"Active model set to: {model_name}")
    
    def get_active_model_path(self) -> str:
        """Get the path of the currently active model"""
        config = self.get_config()
        active_model = config["active_model"]
        return config["models"][active_model]["path"]
    
    def list_models(self):
        """List all available models"""
        config = self.get_config()
        
        print("Available models:")
        for name, info in config["models"].items():
            status = "🟢 ACTIVE" if name == config["active_model"] else "⚪"
            print(f"  {status} {name}")
            print(f"    Path: {info['path']}")
            print(f"    Description: {info.get('description', 'No description')}")
            print(f"    Created: {info.get('created', 'Unknown')}")
            print()
    
    def backup_original_model(self):
        """Backup the original model to models directory"""
        original_path = "./model/snapshots/979938bf89ccdc949c0131ddd3841e24578a4742"
        backup_path = os.path.join(self.models_dir, "original")
        
        if os.path.exists(original_path) and not os.path.exists(os.path.join(backup_path, "config.json")):
            print("Backing up original model...")
            shutil.copytree(original_path, backup_path, dirs_exist_ok=True)
            print(f"Original model backed up to {backup_path}")
    
    def install_fine_tuned_model(self, fine_tuned_path: str):
        """Install a fine-tuned model"""
        if not os.path.exists(fine_tuned_path):
            raise FileNotFoundError(f"Fine-tuned model not found: {fine_tuned_path}")
        
        # Backup original model first
        self.backup_original_model()
        
        # Copy fine-tuned model to models directory
        target_path = os.path.join(self.models_dir, "fine_tuned")
        if os.path.exists(target_path):
            shutil.rmtree(target_path)
        
        shutil.copytree(fine_tuned_path, target_path)
        
        # Register the fine-tuned model
        self.register_model(
            "fine_tuned", 
            target_path, 
            "Fine-tuned model for improved OCR performance"
        )
        
        print(f"Fine-tuned model installed to {target_path}")
    
    def test_model(self, model_name: str = None, test_image: str = "test.jpg"):
        """Test a model with a sample image"""
        if model_name is None:
            model_path = self.get_active_model_path()
            model_name = self.get_config()["active_model"]
        else:
            config = self.get_config()
            model_path = config["models"][model_name]["path"]
        
        if not os.path.exists(test_image):
            print(f"Test image not found: {test_image}")
            return
        
        print(f"Testing model '{model_name}' with {test_image}...")
        
        try:
            # Load model and tokenizer
            tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
            model = AutoModel.from_pretrained(model_path, trust_remote_code=True).eval()
            
            if torch.cuda.is_available():
                model = model.to('cuda')
            
            # Process image
            result = model.chat(tokenizer, test_image, ocr_type='ocr')
            
            print(f"✅ Model test successful!")
            print(f"Extracted text: {result[:200]}...")
            
        except Exception as e:
            print(f"❌ Model test failed: {e}")
    
    def compare_models(self, test_image: str = "test.jpg"):
        """Compare performance of all available models"""
        if not os.path.exists(test_image):
            print(f"Test image not found: {test_image}")
            return
        
        config = self.get_config()
        results = {}
        
        print(f"Comparing models with {test_image}...")
        print("=" * 50)
        
        for model_name in config["models"]:
            try:
                print(f"\nTesting {model_name}...")
                model_path = config["models"][model_name]["path"]
                
                # Load model
                tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
                model = AutoModel.from_pretrained(model_path, trust_remote_code=True).eval()
                
                if torch.cuda.is_available():
                    model = model.to('cuda')
                
                # Process image
                import time
                start_time = time.time()
                result = model.chat(tokenizer, test_image, ocr_type='ocr')
                processing_time = time.time() - start_time
                
                results[model_name] = {
                    "text": result,
                    "time": processing_time,
                    "success": True
                }
                
                print(f"✅ Success ({processing_time:.2f}s)")
                print(f"Text: {result[:100]}...")
                
            except Exception as e:
                results[model_name] = {
                    "error": str(e),
                    "success": False
                }
                print(f"❌ Failed: {e}")
        
        # Summary
        print("\n" + "=" * 50)
        print("COMPARISON SUMMARY:")
        for model_name, result in results.items():
            if result["success"]:
                print(f"✅ {model_name}: {result['time']:.2f}s")
            else:
                print(f"❌ {model_name}: Failed")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Manage OCR models")
    parser.add_argument("action", choices=["list", "set", "register", "install", "test", "compare", "backup"])
    parser.add_argument("--model", help="Model name")
    parser.add_argument("--path", help="Model path")
    parser.add_argument("--description", help="Model description")
    parser.add_argument("--test-image", default="test.jpg", help="Test image path")
    
    args = parser.parse_args()
    
    manager = ModelManager()
    
    if args.action == "list":
        manager.list_models()
        
    elif args.action == "set":
        if not args.model:
            print("--model is required")
            return
        manager.set_active_model(args.model)
        
    elif args.action == "register":
        if not args.model or not args.path:
            print("--model and --path are required")
            return
        manager.register_model(args.model, args.path, args.description or "")
        
    elif args.action == "install":
        if not args.path:
            print("--path is required")
            return
        manager.install_fine_tuned_model(args.path)
        
    elif args.action == "test":
        manager.test_model(args.model, args.test_image)
        
    elif args.action == "compare":
        manager.compare_models(args.test_image)
        
    elif args.action == "backup":
        manager.backup_original_model()

if __name__ == "__main__":
    main()
