# ocr_processor.py - Simplified Basic OCR Only
import os
import torch
from transformers import AutoModel, AutoTokenizer
from flask import Flask, request, jsonify
from flask_cors import CORS
from werkzeug.utils import secure_filename
import tempfile
import shutil

# Configuration - No new dependencies
MODEL_PATH = "./model/snapshots/979938bf89ccdc949c0131ddd3841e24578a4742"
os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configure uploads
UPLOAD_FOLDER = tempfile.mkdtemp()
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'tif', 'tiff'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload

# Model initialization
tokenizer = None
model = None

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def initialize_model():
    global tokenizer, model

    try:
        # Verify model files first
        required_files = ["config.json", "model.safetensors", "tokenizer_config.json"]
        for f in required_files:
            if not os.path.exists(os.path.join(MODEL_PATH, f)):
                raise FileNotFoundError(f"Missing: {os.path.join(MODEL_PATH, f)}")

        # Load components
        tokenizer = AutoTokenizer.from_pretrained(
            MODEL_PATH,
            trust_remote_code=True,
            local_files_only=True,
            pad_token_id=151643  # Explicitly set from your error message
        )

        model = AutoModel.from_pretrained(
            MODEL_PATH,
            trust_remote_code=True,
            device_map='cuda' if torch.cuda.is_available() else 'cpu',
            low_cpu_mem_usage=True,
            use_safetensors=True,
            local_files_only=True
        ).eval()

        if torch.cuda.is_available():
            model = model.to('cuda')
            print(f"✅ Loaded on {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  Using CPU")

        return True

    except Exception as e:
        print(f"❌ Setup failed: {str(e)}")
        return False

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        "status": "ok",
        "model_loaded": tokenizer is not None and model is not None
    })

@app.route('/ocr', methods=['POST'])
def process_ocr():
    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    if not allowed_file(file.filename):
        return jsonify({"error": f"File type not allowed. Supported types: {', '.join(ALLOWED_EXTENSIONS)}"}), 400

    try:
        # Save uploaded file
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

        # Process the image with basic OCR only
        print(f"Processing image: {filename}")
        res = model.chat(tokenizer, filepath, ocr_type='ocr')

        # Clean up uploaded image
        os.remove(filepath)

        return jsonify({
            "text": res
        })

    except Exception as e:
        import traceback
        print(f"Error processing OCR request: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            "error": str(e)
        }), 500

# Direct processing function for basic OCR
def process_image(image_path):
    """
    Process an image with basic OCR only

    Args:
        image_path (str): Path to the image file

    Returns:
        str: The OCR result text
    """
    # Ensure model is loaded
    if model is None or tokenizer is None:
        if not initialize_model():
            print("Failed to initialize model")
            return None

    try:
        # Process with basic OCR only
        result = model.chat(tokenizer, image_path, ocr_type='ocr')
        return result

    except Exception as e:
        print(f"Error processing image: {str(e)}")
        return None

# Initialize on startup if running directly
if __name__ == "__main__":
    if initialize_model():
        # Clean up temp folder on exit
        try:
            app.config['PORT'] = 5000
            print("Server starting on http://localhost:5000")
            app.run(host='0.0.0.0', port=app.config['PORT'], debug=False)
        finally:
            shutil.rmtree(UPLOAD_FOLDER, ignore_errors=True)
    else:
        print("Failed to initialize model. Exiting.")
else:
    # For WSGI servers, we'll initialize on first import
    initialize_model()