# ocr_processor.py
import os
import torch
from transformers import AutoModel, AutoTokenizer
import json
from flask import Flask, request, jsonify, redirect, send_file
from flask_cors import CORS
from werkzeug.utils import secure_filename
import tempfile
import shutil
import webbrowser

# Configuration - No new dependencies
MODEL_PATH = "./model/snapshots/979938bf89ccdc949c0131ddd3841e24578a4742"
os.environ['HF_HUB_DISABLE_SYMLINKS_WARNING'] = '1'

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configure uploads
UPLOAD_FOLDER = tempfile.mkdtemp()
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'tif', 'tiff'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload

# Model initialization
tokenizer = None
model = None

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def initialize_model():
    global tokenizer, model
    
    try:
        # Verify model files first
        required_files = ["config.json", "model.safetensors", "tokenizer_config.json"]
        for f in required_files:
            if not os.path.exists(os.path.join(MODEL_PATH, f)):
                raise FileNotFoundError(f"Missing: {os.path.join(MODEL_PATH, f)}")

        # Load components
        tokenizer = AutoTokenizer.from_pretrained(
            MODEL_PATH,
            trust_remote_code=True,
            local_files_only=True,
            pad_token_id=151643  # Explicitly set from your error message
        )
        
        model = AutoModel.from_pretrained(
            MODEL_PATH,
            trust_remote_code=True,
            device_map='cuda' if torch.cuda.is_available() else 'cpu',
            low_cpu_mem_usage=True,
            use_safetensors=True,
            local_files_only=True
        ).eval()

        if torch.cuda.is_available():
            model = model.to('cuda')
            print(f"✅ Loaded on {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  Using CPU")
            
        return True

    except Exception as e:
        print(f"❌ Setup failed: {str(e)}")
        return False

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        "status": "ok",
        "model_loaded": tokenizer is not None and model is not None
    })

@app.route('/rendered/<filename>')
def rendered_file(filename):
    """Serve the rendered HTML files"""
    return send_file(os.path.join(app.config['UPLOAD_FOLDER'], filename))

@app.route('/ocr', methods=['POST'])
def process_ocr():
    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400
        
    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400
        
    if not allowed_file(file.filename):
        return jsonify({"error": f"File type not allowed. Supported types: {', '.join(ALLOWED_EXTENSIONS)}"}), 400
        
    # Get parameters with defaults
    ocr_type = request.form.get('ocr_type', 'ocr')  # 'ocr' or 'format'
    ocr_box = request.form.get('ocr_box', '')
    ocr_color = request.form.get('ocr_color', '')
    use_crop = request.form.get('use_crop', 'false').lower() == 'true'
    render = request.form.get('render', 'false').lower() == 'true'
    open_browser = request.form.get('open_browser', 'false').lower() == 'true'
    
    try:
        # Save uploaded file
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Process the image
        if use_crop:
            # Use multi-crop OCR mode
            print(f"Processing with multi-crop OCR: type={ocr_type}")
            res = model.chat_crop(tokenizer, filepath, ocr_type=ocr_type)
        else:
            # Process with standard OCR
            kwargs = {
                'ocr_type': ocr_type
            }
            
            # Add optional parameters if provided
            if ocr_box:
                print(f"Using OCR box: {ocr_box}")
                kwargs['ocr_box'] = ocr_box
            
            if ocr_color:
                print(f"Using OCR color: {ocr_color}")
                kwargs['ocr_color'] = ocr_color
            
            if render:
                # Setup rendering
                render_filename = f"render_{os.path.splitext(filename)[0]}.html"
                render_file = os.path.join(app.config['UPLOAD_FOLDER'], render_filename)
                
                # Use demo.html as well for direct access
                demo_file = './demo.html'
                
                # Add rendering parameters
                kwargs['render'] = True
                kwargs['save_render_file'] = render_file
                
                print(f"Processing with rendering: {kwargs}")
                
                # Process with rendering
                res = model.chat(tokenizer, filepath, **kwargs)
                
                # Copy the render file to demo.html for direct access
                shutil.copy2(render_file, demo_file)
                
                # Read the HTML content
                with open(render_file, 'r') as f:
                    html_content = f.read()
                
                # Open browser if requested
                if open_browser:
                    # Use the absolute path to the demo.html file
                    demo_absolute_path = os.path.abspath(demo_file)
                    print(f"Opening browser with: {demo_absolute_path}")
                    webbrowser.open(f"file:///{demo_absolute_path}")
                    
                # Return both text and HTML
                return jsonify({
                    "text": res,
                    "html": html_content,
                    "render_url": f"/rendered/{render_filename}",
                    "local_file": os.path.abspath(demo_file)
                })
            else:
                # Standard OCR without rendering
                print(f"Processing with standard OCR: {kwargs}")
                res = model.chat(tokenizer, filepath, **kwargs)
        
        # Clean up uploaded image (but keep rendered HTML)
        os.remove(filepath)
        
        return jsonify({
            "text": res
        })
        
    except Exception as e:
        import traceback
        print(f"Error processing OCR request: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            "error": str(e)
        }), 500

# Direct processing function
def process_image(image_path, ocr_type='ocr', ocr_box='', ocr_color='', use_crop=False, 
                 render=False, output_html='./demo.html', open_browser=False):
    """
    Process an image with all OCR options
    
    Args:
        image_path (str): Path to the image file
        ocr_type (str): Type of OCR - 'ocr' or 'format'
        ocr_box (str): Coordinates for fine-grained OCR
        ocr_color (str): Color values for OCR
        use_crop (bool): Whether to use multi-crop mode
        render (bool): Whether to render the output
        output_html (str): Path to save the rendered HTML
        open_browser (bool): Whether to open the result in browser
        
    Returns:
        str: The OCR result text
    """
    # Ensure model is loaded
    if model is None or tokenizer is None:
        if not initialize_model():
            print("Failed to initialize model")
            return None
    
    try:
        # Process with the selected options
        kwargs = {'ocr_type': ocr_type}
        
        if ocr_box:
            kwargs['ocr_box'] = ocr_box
            
        if ocr_color:
            kwargs['ocr_color'] = ocr_color
            
        if render:
            kwargs['render'] = True
            kwargs['save_render_file'] = output_html
        
        # Process the image
        if use_crop:
            result = model.chat_crop(tokenizer, image_path, **kwargs)
        else:
            result = model.chat(tokenizer, image_path, **kwargs)
            
        # Open browser if rendering and browser opening is requested
        if render and open_browser:
            webbrowser.open(os.path.abspath(output_html))
            print(f"Opened {os.path.abspath(output_html)} in browser")
            
        return result
        
    except Exception as e:
        print(f"Error processing image: {str(e)}")
        return None

# Command-line processing example
def run_ocr_example():
    """Example of using the OCR processor directly"""
    # Plain text OCR
    # result = process_image('test.jpg', ocr_type='ocr')
    
    # Formatted OCR
    # result = process_image('test.jpg', ocr_type='format')
    
    # Fine-grained OCR with box coordinates
    # result = process_image('test.jpg', ocr_type='ocr', ocr_box='100,100,300,300')
    
    # Multi-crop OCR
    # result = process_image('test.jpg', ocr_type='format', use_crop=True)
    
    # Rendered OCR with browser
    result = process_image('test.jpg', ocr_type='format', render=True, open_browser=True)
    
    print(f"OCR Result: {result[:500]}...")

# Initialize on startup if running directly
if __name__ == "__main__":
    if initialize_model():
        # Clean up temp folder on exit
        try:
            app.config['PORT'] = 5000
            print("Server starting on http://localhost:5000")
            app.run(host='0.0.0.0', port=app.config['PORT'], debug=False)
        finally:
            shutil.rmtree(UPLOAD_FOLDER, ignore_errors=True)
    else:
        print("Failed to initialize model. Exiting.")
else:
    # For WSGI servers, we'll initialize on first import
    initialize_model()