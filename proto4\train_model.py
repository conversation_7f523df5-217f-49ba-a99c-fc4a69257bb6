#!/usr/bin/env python
# train_model.py - Fine-tune the Qwen Vision model for better OCR performance

import os
import torch
import json
from torch.utils.data import Dataset, DataLoader
from transformers import (
    AutoModel, 
    AutoTokenizer, 
    Trainer, 
    TrainingArguments,
    DataCollatorWithPadding
)
from PIL import Image
import logging
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OCRDataset(Dataset):
    """Dataset class for OCR training data"""
    
    def __init__(self, data_dir: str, tokenizer, max_length: int = 512):
        """
        Initialize OCR dataset
        
        Args:
            data_dir: Directory containing images and annotations
            tokenizer: Tokenizer for text processing
            max_length: Maximum sequence length
        """
        self.data_dir = data_dir
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.samples = self._load_samples()
        
    def _load_samples(self) -> List[Dict[str, Any]]:
        """Load training samples from data directory"""
        samples = []
        
        # Expected structure:
        # data_dir/
        #   ├── images/
        #   │   ├── image1.jpg
        #   │   ├── image2.png
        #   │   └── ...
        #   └── annotations.json
        
        annotations_path = os.path.join(self.data_dir, "annotations.json")
        if not os.path.exists(annotations_path):
            logger.error(f"Annotations file not found: {annotations_path}")
            return samples
            
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
            
        for item in annotations:
            image_path = os.path.join(self.data_dir, "images", item["image"])
            if os.path.exists(image_path):
                samples.append({
                    "image_path": image_path,
                    "text": item["text"]
                })
            else:
                logger.warning(f"Image not found: {image_path}")
                
        logger.info(f"Loaded {len(samples)} training samples")
        return samples
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        
        # Load and process image
        image = Image.open(sample["image_path"]).convert("RGB")
        
        # Tokenize text
        text_encoding = self.tokenizer(
            sample["text"],
            max_length=self.max_length,
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )
        
        return {
            "image": image,
            "input_ids": text_encoding["input_ids"].squeeze(),
            "attention_mask": text_encoding["attention_mask"].squeeze(),
            "labels": text_encoding["input_ids"].squeeze()
        }

class OCRTrainer:
    """Trainer class for fine-tuning OCR model"""
    
    def __init__(self, model_path: str, output_dir: str = "./fine_tuned_model"):
        """
        Initialize trainer
        
        Args:
            model_path: Path to pre-trained model
            output_dir: Directory to save fine-tuned model
        """
        self.model_path = model_path
        self.output_dir = output_dir
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Load model and tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            model_path, 
            trust_remote_code=True
        )
        self.model = AutoModel.from_pretrained(
            model_path, 
            trust_remote_code=True
        )
        
        # Move model to device
        self.model.to(self.device)
        
        logger.info(f"Model loaded on {self.device}")
        
    def prepare_training_args(self, 
                            num_epochs: int = 3,
                            batch_size: int = 4,
                            learning_rate: float = 5e-5) -> TrainingArguments:
        """Prepare training arguments"""
        
        return TrainingArguments(
            output_dir=self.output_dir,
            num_train_epochs=num_epochs,
            per_device_train_batch_size=batch_size,
            per_device_eval_batch_size=batch_size,
            learning_rate=learning_rate,
            warmup_steps=100,
            logging_steps=10,
            save_steps=500,
            eval_steps=500,
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            dataloader_pin_memory=False,
            remove_unused_columns=False,
            fp16=torch.cuda.is_available(),  # Use mixed precision if GPU available
        )
    
    def train(self, 
              train_dataset: OCRDataset, 
              eval_dataset: OCRDataset = None,
              **training_kwargs):
        """
        Fine-tune the model
        
        Args:
            train_dataset: Training dataset
            eval_dataset: Evaluation dataset (optional)
            **training_kwargs: Additional training arguments
        """
        
        # Prepare training arguments
        training_args = self.prepare_training_args(**training_kwargs)
        
        # Data collator
        data_collator = DataCollatorWithPadding(tokenizer=self.tokenizer)
        
        # Initialize trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            data_collator=data_collator,
            tokenizer=self.tokenizer,
        )
        
        # Start training
        logger.info("Starting training...")
        trainer.train()
        
        # Save the fine-tuned model
        trainer.save_model()
        self.tokenizer.save_pretrained(self.output_dir)
        
        logger.info(f"Model saved to {self.output_dir}")
        
        return trainer

def create_sample_dataset():
    """Create a sample dataset structure for demonstration"""
    
    sample_dir = "./sample_training_data"
    os.makedirs(f"{sample_dir}/images", exist_ok=True)
    
    # Create sample annotations.json
    sample_annotations = [
        {
            "image": "sample1.jpg",
            "text": "This is sample text from image 1"
        },
        {
            "image": "sample2.png", 
            "text": "Another example of text extraction"
        }
    ]
    
    with open(f"{sample_dir}/annotations.json", 'w') as f:
        json.dump(sample_annotations, f, indent=2)
    
    print(f"Sample dataset structure created at {sample_dir}")
    print("Add your images to the 'images' folder and update annotations.json")

def main():
    """Main training function"""
    
    # Configuration
    MODEL_PATH = "./model/snapshots/979938bf89ccdc949c0131ddd3841e24578a4742"
    DATA_DIR = "./training_data"  # Your training data directory
    OUTPUT_DIR = "./fine_tuned_ocr_model"
    
    # Check if training data exists
    if not os.path.exists(DATA_DIR):
        print(f"Training data directory not found: {DATA_DIR}")
        print("Creating sample dataset structure...")
        create_sample_dataset()
        return
    
    try:
        # Initialize trainer
        trainer = OCRTrainer(MODEL_PATH, OUTPUT_DIR)
        
        # Load datasets
        train_dataset = OCRDataset(DATA_DIR, trainer.tokenizer)
        
        if len(train_dataset) == 0:
            print("No training samples found. Please add data to the training directory.")
            return
        
        # Split dataset (80% train, 20% eval)
        train_size = int(0.8 * len(train_dataset))
        eval_size = len(train_dataset) - train_size
        
        train_dataset, eval_dataset = torch.utils.data.random_split(
            train_dataset, [train_size, eval_size]
        )
        
        # Start training
        trainer.train(
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            num_epochs=3,
            batch_size=2,  # Small batch size for limited GPU memory
            learning_rate=5e-5
        )
        
        print(f"Training completed! Fine-tuned model saved to {OUTPUT_DIR}")
        
    except Exception as e:
        logger.error(f"Training failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
