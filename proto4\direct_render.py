#!/usr/bin/env python
# direct_render.py - Helper script to process an image and save rendered HTML

import os
import torch
import argparse
from transformers import AutoModel, AutoTokenizer

def render_ocr_image(image_path, output_html="./demo.html", ocr_type="format"):
    """
    Process an image with OCR and save the rendered HTML output
    
    Args:
        image_path: Path to the image file
        output_html: Path where to save the HTML output (default: ./demo.html)
        ocr_type: Type of OCR to perform (default: format)
    
    Returns:
        str: The extracted text
    """
    # Model path
    MODEL_PATH = "./model/snapshots/979938bf89ccdc949c0131ddd3841e24578a4742"
    
    print(f"Loading model from {MODEL_PATH}...")
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        MODEL_PATH,
        trust_remote_code=True,
        local_files_only=True
    )
    
    # Load model
    model = AutoModel.from_pretrained(
        MODEL_PATH,
        trust_remote_code=True,
        device_map='cuda' if torch.cuda.is_available() else 'cpu',
        use_safetensors=True,
        local_files_only=True
    ).eval()
    
    print(f"Processing image: {image_path}")
    
    # Process the image and save HTML rendering
    result = model.chat(
        tokenizer, 
        image_path, 
        ocr_type=ocr_type, 
        render=True, 
        save_render_file=output_html
    )
    
    print(f"\nOCR Result:\n{result[:500]}{'...' if len(result) > 500 else ''}\n")
    print(f"HTML rendering saved to: {os.path.abspath(output_html)}")
    print(f"Open this file in your browser to view the formatted output.")
    
    return result

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process an image with OCR and save rendered HTML")
    parser.add_argument("image_path", help="Path to the image file")
    parser.add_argument("--output", default="./demo.html", help="Path where to save the HTML output")
    parser.add_argument("--type", choices=["ocr", "format"], default="format", help="Type of OCR to perform")
    
    args = parser.parse_args()
    
    render_ocr_image(args.image_path, args.output, args.type)