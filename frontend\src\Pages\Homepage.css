.glass-morphism {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.perspective-1000 {
  perspective: 1000px;
}

.upload-area {
  transition: transform 0.3s ease;
  transform-style: preserve-3d;
}

.upload-area:hover {
  transform: translateZ(20px);
}

.results-container {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
}

.results-container::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shine 1.5s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.glassmorphism {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
}

.perspective {
  perspective: 2000px;
  transform-style: preserve-3d;
}

.bg-grid-pattern {
  background-image: linear-gradient(
      to right,
      rgba(99, 102, 241, 0.1) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
  background-size: 14px 14px;
}

.bg-grid-white {
  background-size: 30px 30px;
  background-image: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
}

/* Advanced animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.floating {
  animation: float 6s ease-in-out infinite;
}

/* Glowing effect */
.glow {
  position: relative;
}

.glow::after {
  content: "";
  position: absolute;
  inset: -1px;
  background: linear-gradient(45deg, #4f46e5, #7c3aed, #2563eb);
  filter: blur(15px);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glow:hover::after {
  opacity: 1;
}

.glowing-border {
  position: relative;
}

.glowing-border::before {
  content: "";
  position: absolute;
  inset: -1px;
  background: linear-gradient(var(--rotate), #5b21b6, #4f46e5, #2563eb);
  animation: rotate 4s linear infinite;
  border-radius: inherit;
  z-index: -1;
}

@property --rotate {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

@keyframes rotate {
  from {
    --rotate: 0deg;
  }
  to {
    --rotate: 360deg;
  }
}

.glass-effect {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 25px 45px rgba(0, 0, 0, 0.2);
}

/* Advanced animation keyframes */
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Neo-Brutalism/Retro Futurism Style */

.neo-brutalism {
  --neo-primary: rgb(99, 102, 241);
  --neo-primary-dark: rgb(79, 70, 229);
  --neo-highlight: rgb(217, 70, 239);
  --neo-background: #0a0a0c;
  --neo-box: rgb(24, 24, 27);
  --neo-box-hover: rgb(39, 39, 42);
  --neo-text: rgb(244, 244, 245);
  --neo-shadow-offset: 5px;

  background-color: var(--neo-background);
  color: var(--neo-text);
  min-height: 100vh;
  font-family: "Inter", system-ui, -apple-system, sans-serif;
}

/* Grid Background */
.neo-grid {
  position: absolute;
  inset: 0;
  background-size: 50px 50px;
  background-image: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.03) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  opacity: 0.5;
}

/* Glitchy title */
.glitch-text {
  position: relative;
  display: inline-block;
  letter-spacing: -2px;
  background: linear-gradient(to right, #6366f1, #d946ef);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.glitch-text::before,
.glitch-text::after {
  content: "NEURAL OCR";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, #6366f1, #d946ef);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.glitch-text::before {
  animation: glitch 2s infinite;
  clip-path: polygon(0 0, 100% 0, 100% 45%, 0 45%);
  transform: translate(-2px, -2px);
}

.glitch-text::after {
  animation: glitch 1s infinite;
  clip-path: polygon(0 60%, 100% 60%, 100% 100%, 0 100%);
  transform: translate(2px, 2px);
}

@keyframes glitch {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-3px, 3px);
  }
  40% {
    transform: translate(-3px, -3px);
  }
  60% {
    transform: translate(3px, 3px);
  }
  80% {
    transform: translate(3px, -3px);
  }
  100% {
    transform: translate(0);
  }
}

/* Main content boxes */
.neo-box {
  background-color: var(--neo-box);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 5px 5px 0px var(--neo-primary);
  transition: all 0.25s cubic-bezier(0.25, 1, 0.5, 1);
  overflow: hidden;
}

.neo-box:hover {
  box-shadow: 8px 8px 0px var(--neo-primary);
  transform: translate(-2px, -2px);
}

.neo-box-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.neo-badge {
  background-color: var(--neo-primary);
  font-size: 12px;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: white;
}

.neo-badge.result {
  background-color: var(--neo-highlight);
}

/* Upload section */
.upload-container {
  height: 250px;
  margin: 1.5rem;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s ease;
}

.upload-container:hover {
  border-color: var(--neo-primary);
  background-color: rgba(99, 102, 241, 0.05);
}

.upload-container.active {
  border-color: var(--neo-primary);
  background-color: rgba(99, 102, 241, 0.1);
  transform: scale(0.98);
}

.upload-container.error {
  border-color: rgb(239, 68, 68);
  background-color: rgba(239, 68, 68, 0.05);
}

.upload-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  text-align: center;
}

.upload-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(99, 102, 241, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  color: var(--neo-primary);
}

.upload-text {
  font-weight: 500;
  color: rgb(212, 212, 216);
  margin-bottom: 0.5rem;
}

.upload-hint {
  font-size: 0.875rem;
  color: rgb(113, 113, 122);
}

/* Settings section */
.settings-container {
  padding: 0 1.5rem 1.5rem;
}

.setting-section {
  margin-bottom: 1.5rem;
}

.setting-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgb(161, 161, 170);
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.neo-tabs {
  display: flex;
  background-color: rgba(39, 39, 42, 0.5);
  border-radius: 8px;
  padding: 0.25rem;
  margin-bottom: 1rem;
}

.neo-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.625rem;
  border-radius: 6px;
  font-size: 0.875rem;
  color: rgb(161, 161, 170);
  transition: all 0.15s ease;
}

.neo-tab:hover {
  color: rgb(212, 212, 216);
}

.neo-tab.active {
  background-color: var(--neo-primary);
  color: white;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 0.75rem;
}

@media (min-width: 640px) {
  .options-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.neo-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(39, 39, 42, 0.5);
  padding: 0.75rem 1rem;
  border-radius: 8px;
  transition: background-color 0.15s ease;
  cursor: pointer;
}

.neo-toggle:hover {
  background-color: rgba(39, 39, 42, 0.8);
}

.neo-toggle span {
  font-size: 0.875rem;
  color: rgb(212, 212, 216);
}

.toggle-wrapper {
  position: relative;
  width: 36px;
  height: 20px;
}

.toggle-wrapper input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgb(63, 63, 70);
  transition: 0.4s;
  border-radius: 34px;
}

.toggle-slider::before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--neo-primary);
}

input:checked + .toggle-slider::before {
  transform: translateX(16px);
}

/* Action buttons */
.neo-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 2rem;
}

@media (min-width: 640px) {
  .neo-actions {
    flex-direction: row;
  }
}

.neo-button {
  font-weight: 600;
  font-size: 0.95rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s cubic-bezier(0.25, 1, 0.5, 1);
  cursor: pointer;
}

.neo-button.primary {
  background-color: var(--neo-primary);
  color: white;
  flex: 2;
  box-shadow: 4px 4px 0px var(--neo-primary-dark);
}

.neo-button.primary:hover {
  box-shadow: 6px 6px 0px var(--neo-primary-dark);
  transform: translate(-2px, -2px);
}

.neo-button.primary:active {
  box-shadow: 2px 2px 0px var(--neo-primary-dark);
  transform: translate(2px, 2px);
}

.neo-button.primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.neo-button.secondary {
  background-color: transparent;
  color: rgb(212, 212, 216);
  border: 1px solid rgba(255, 255, 255, 0.1);
  flex: 1;
}

.neo-button.secondary:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Results section */
.result-container {
  padding: 1.5rem;
  min-height: 400px;
}

.processing-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
}

.scanner {
  width: 200px;
  height: 4px;
  background-color: var(--neo-primary);
  position: relative;
  margin-bottom: 2rem;
  overflow: hidden;
}

.scanner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.8),
    transparent
  );
  animation: scan 1.5s linear infinite;
}

@keyframes scan {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.processing-text {
  font-size: 1rem;
  color: rgb(161, 161, 170);
  text-align: center;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.copy-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: rgb(161, 161, 170);
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  background-color: rgba(39, 39, 42, 0.5);
  transition: all 0.15s ease;
}

.copy-button:hover {
  background-color: rgba(39, 39, 42, 0.8);
  color: rgb(212, 212, 216);
}

.code-display {
  margin-bottom: 1.5rem;
  border-radius: 0.5rem;
  overflow: hidden;
  max-height: 300px;
  overflow-y: auto;
}

.divider {
  position: relative;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 2rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.divider span {
  position: absolute;
  padding: 0 1rem;
  background-color: var(--neo-box);
  color: rgb(161, 161, 170);
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.rendered-output {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
}

.rendered-content {
  padding: 1.5rem;
  max-height: 300px;
  overflow-y: auto;
  color: #333;
}

.view-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--neo-primary);
  padding: 0.75rem 1.5rem;
  background-color: rgba(99, 102, 241, 0.1);
  justify-content: center;
  transition: background-color 0.15s ease;
}

.view-button:hover {
  background-color: rgba(99, 102, 241, 0.2);
}

/* Landing Page Styles */
.landing-page {
  font-family: "Inter", system-ui, -apple-system, sans-serif;
  color: #334155;
}

.bg-grid-pattern {
  background-size: 30px 30px;
  background-image: linear-gradient(
      to right,
      rgba(99, 102, 241, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(to bottom, rgba(99, 102, 241, 0.05) 1px, transparent 1px);
  opacity: 0.5;
}

.landing-preview {
  position: relative;
  perspective: 1000px;
}

.landing-preview::after {
  content: "";
  position: absolute;
  top: -20px;
  right: -20px;
  width: 100px;
  height: 100px;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 70C30 80 70 80 80 70' stroke='%234F46E5' stroke-width='4' stroke-linecap='round'/%3E%3Cpath d='M20 50C30 60 70 60 80 50' stroke='%234F46E5' stroke-width='4' stroke-linecap='round'/%3E%3Cpath d='M20 30C30 40 70 40 80 30' stroke='%234F46E5' stroke-width='4' stroke-linecap='round'/%3E%3C/svg%3E")
    no-repeat;
  opacity: 0.5;
  z-index: 1;
}

.neo-box {
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.neo-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.07),
    0 20px 25px -5px rgba(0, 0, 0, 0.15);
}

.feature-card {
  transition: all 0.3s ease;
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.active-feature {
  border-color: rgba(79, 70, 229, 0.5);
  box-shadow: 0 10px 25px rgba(79, 70, 229, 0.1);
}

.step-card {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  padding-top: 3rem;
}

.step-number {
  position: absolute;
  top: -1rem;
  left: 2rem;
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.cta-card {
  overflow: hidden;
  position: relative;
}

.cta-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M50 100C77.6142 100 100 77.6142 100 50C100 22.3858 77.6142 0 50 0C22.3858 0 0 22.3858 0 50C0 77.6142 22.3858 100 50 100ZM50 80C66.5685 80 80 66.5685 80 50C80 33.4315 66.5685 20 50 20C33.4315 20 20 33.4315 20 50C20 66.5685 33.4315 80 50 80Z' fill='white' fill-opacity='0.1'/%3E%3C/svg%3E");
  opacity: 0.1;
  z-index: 0;
}

.stat-item {
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 10%;
  right: -4px;
  height: 80%;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.2);
  display: none;
}

@media (min-width: 768px) {
  .stat-item:not(:last-child)::after {
    display: block;
  }
}
