#!/usr/bin/env python
# prepare_training_data.py - Prepare training data for OCR model fine-tuning

import os
import json
import shutil
from PIL import Image
import argparse
from typing import List, Dict, Any

def create_training_structure(base_dir: str = "./training_data"):
    """Create the required directory structure for training"""
    
    os.makedirs(f"{base_dir}/images", exist_ok=True)
    
    # Create sample annotations.json with proper format
    sample_annotations = [
        {
            "image": "example1.jpg",
            "text": "Sample text that should be extracted from this image"
        },
        {
            "image": "example2.png", 
            "text": "Another example of text that the model should learn to extract"
        }
    ]
    
    annotations_path = f"{base_dir}/annotations.json"
    if not os.path.exists(annotations_path):
        with open(annotations_path, 'w', encoding='utf-8') as f:
            json.dump(sample_annotations, f, indent=2, ensure_ascii=False)
    
    print(f"Training data structure created at: {base_dir}")
    print(f"├── images/          # Put your training images here")
    print(f"└── annotations.json # Update this with your image-text pairs")

def validate_images(images_dir: str) -> List[str]:
    """Validate and list all valid image files"""
    
    valid_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.tiff', '.tif'}
    valid_images = []
    
    if not os.path.exists(images_dir):
        print(f"Images directory not found: {images_dir}")
        return valid_images
    
    for filename in os.listdir(images_dir):
        file_path = os.path.join(images_dir, filename)
        if os.path.isfile(file_path):
            _, ext = os.path.splitext(filename.lower())
            if ext in valid_extensions:
                try:
                    # Try to open image to validate
                    with Image.open(file_path) as img:
                        img.verify()
                    valid_images.append(filename)
                except Exception as e:
                    print(f"Invalid image {filename}: {e}")
    
    return valid_images

def validate_annotations(annotations_path: str, valid_images: List[str]) -> Dict[str, Any]:
    """Validate annotations file and check consistency with images"""
    
    if not os.path.exists(annotations_path):
        print(f"Annotations file not found: {annotations_path}")
        return {"valid": False, "errors": ["Annotations file missing"]}
    
    try:
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
    except json.JSONDecodeError as e:
        return {"valid": False, "errors": [f"Invalid JSON: {e}"]}
    
    errors = []
    valid_annotations = []
    
    if not isinstance(annotations, list):
        errors.append("Annotations should be a list of objects")
        return {"valid": False, "errors": errors}
    
    for i, item in enumerate(annotations):
        if not isinstance(item, dict):
            errors.append(f"Item {i}: Should be an object")
            continue
            
        if "image" not in item:
            errors.append(f"Item {i}: Missing 'image' field")
            continue
            
        if "text" not in item:
            errors.append(f"Item {i}: Missing 'text' field")
            continue
            
        if item["image"] not in valid_images:
            errors.append(f"Item {i}: Image '{item['image']}' not found in images directory")
            continue
            
        if not isinstance(item["text"], str) or len(item["text"].strip()) == 0:
            errors.append(f"Item {i}: Text should be a non-empty string")
            continue
            
        valid_annotations.append(item)
    
    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "valid_count": len(valid_annotations),
        "total_count": len(annotations)
    }

def auto_generate_annotations(images_dir: str, output_path: str):
    """Auto-generate annotations template from existing images"""
    
    valid_images = validate_images(images_dir)
    
    if not valid_images:
        print("No valid images found to generate annotations")
        return
    
    annotations = []
    for image in valid_images:
        annotations.append({
            "image": image,
            "text": f"TODO: Add text content for {image}"
        })
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(annotations, f, indent=2, ensure_ascii=False)
    
    print(f"Generated annotations template for {len(valid_images)} images")
    print(f"Please edit {output_path} and replace 'TODO' entries with actual text")

def convert_from_other_formats(input_dir: str, output_dir: str, format_type: str):
    """Convert from other annotation formats to our format"""
    
    if format_type == "labelme":
        convert_from_labelme(input_dir, output_dir)
    elif format_type == "coco":
        convert_from_coco(input_dir, output_dir)
    else:
        print(f"Unsupported format: {format_type}")

def convert_from_labelme(input_dir: str, output_dir: str):
    """Convert from LabelMe format (JSON files with image annotations)"""
    
    print("Converting from LabelMe format...")
    os.makedirs(f"{output_dir}/images", exist_ok=True)
    
    annotations = []
    
    for filename in os.listdir(input_dir):
        if filename.endswith('.json'):
            json_path = os.path.join(input_dir, filename)
            
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                image_filename = data.get('imagePath', filename.replace('.json', '.jpg'))
                image_path = os.path.join(input_dir, image_filename)
                
                if os.path.exists(image_path):
                    # Copy image
                    shutil.copy2(image_path, f"{output_dir}/images/")
                    
                    # Extract text from shapes
                    text_parts = []
                    for shape in data.get('shapes', []):
                        if 'label' in shape:
                            text_parts.append(shape['label'])
                    
                    if text_parts:
                        annotations.append({
                            "image": image_filename,
                            "text": " ".join(text_parts)
                        })
                        
            except Exception as e:
                print(f"Error processing {filename}: {e}")
    
    # Save annotations
    with open(f"{output_dir}/annotations.json", 'w', encoding='utf-8') as f:
        json.dump(annotations, f, indent=2, ensure_ascii=False)
    
    print(f"Converted {len(annotations)} annotations from LabelMe format")

def main():
    parser = argparse.ArgumentParser(description="Prepare training data for OCR model")
    parser.add_argument("--action", choices=["create", "validate", "generate", "convert"], 
                       required=True, help="Action to perform")
    parser.add_argument("--data-dir", default="./training_data", 
                       help="Training data directory")
    parser.add_argument("--input-dir", help="Input directory for conversion")
    parser.add_argument("--format", choices=["labelme", "coco"], 
                       help="Input format for conversion")
    
    args = parser.parse_args()
    
    if args.action == "create":
        create_training_structure(args.data_dir)
        
    elif args.action == "validate":
        images_dir = os.path.join(args.data_dir, "images")
        annotations_path = os.path.join(args.data_dir, "annotations.json")
        
        print("Validating training data...")
        
        # Validate images
        valid_images = validate_images(images_dir)
        print(f"Found {len(valid_images)} valid images")
        
        # Validate annotations
        result = validate_annotations(annotations_path, valid_images)
        
        if result["valid"]:
            print(f"✅ Validation passed! {result['valid_count']} valid annotations")
        else:
            print("❌ Validation failed:")
            for error in result["errors"]:
                print(f"  - {error}")
                
    elif args.action == "generate":
        images_dir = os.path.join(args.data_dir, "images")
        annotations_path = os.path.join(args.data_dir, "annotations.json")
        auto_generate_annotations(images_dir, annotations_path)
        
    elif args.action == "convert":
        if not args.input_dir or not args.format:
            print("--input-dir and --format are required for conversion")
            return
        convert_from_other_formats(args.input_dir, args.data_dir, args.format)

if __name__ == "__main__":
    main()
