# OCR Backend Service

This project is an Optical Character Recognition (OCR) service built around a Qwen Vision model, providing capabilities for text extraction from images. The system is designed as a REST API for easy integration with various frontend applications.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Project Structure](#project-structure)
- [Setup and Installation](#setup-and-installation)
- [Client-Server Communication](#client-server-communication)
- [API Reference](#api-reference)
- [Rendering OCR Results](#rendering-ocr-results)
- [Technical Details](#technical-details)
- [Troubleshooting](#troubleshooting)

## Overview

This OCR service uses a pre-trained Qwen Vision model to extract text from images, with special features for handling formatted text like tables and mathematical notations. The backend is built with Flask and provides a RESTful API that accepts image uploads and returns the extracted text in either plain or formatted style.

## Features

- **Multiple OCR Modes**:
  - Basic OCR: Extracts plain text from images
  - Formatted OCR: Preserves formatting, especially useful for tables and structured content
- **Multi-crop Processing**: Automatically splits large images into sections for improved OCR accuracy

- **HTML Rendering**: Renders the extracted text in HTML format, preserving formatting

- **RESTful API**: Simple HTTP interface for integration with any client application

- **Cross-Origin Support**: Built-in CORS support for web integrations

## Project Structure

```
project/
├── ocr_processor.py       # Main Flask API application
├── test_api.py            # Script for testing the API
├── client.html            # Web interface for the API
├── demo.html              # Rendered output from OCR in HTML format
├── requirements.txt       # Python dependencies
├── test*.jpg/png          # Test images
└── model/                 # Model files
    └── snapshots/
        └── 979938bf89ccdc949c0131ddd3841e24578a4742/
            ├── config.json
            ├── model.safetensors
            ├── tokenizer_config.json
            └── ...
```

## Setup and Installation

### Prerequisites

- Python 3.8+ (3.10 recommended)
- CUDA-compatible GPU (optional but recommended for performance)
- Git LFS (to handle model files)

### Installation Steps

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd <project-directory>
   ```

2. **Create a virtual environment**:

   ```bash
   python -m venv ocr_env
   ```

3. **Activate the virtual environment**:

   - Windows:
     ```bash
     ocr_env\Scripts\activate
     ```
   - Linux/Mac:
     ```bash
     source ocr_env/bin/activate
     ```

4. **Install dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

5. **Start the server**:
   ```bash
   python ocr_processor.py
   ```
   The server will start on `http://localhost:5000`

## Client-Server Communication

### How the Client Sends Requests

The OCR service uses a standard HTTP multipart/form-data request format to handle file uploads and process parameters. Here's how the client communicates with the backend:

1. **Preparing the Request**:
   The client prepares a POST request with the following components:

   - The image file to be processed (sent as a file upload)
   - OCR options (sent as form fields)

2. **Client Request Flow**:

   ```
   Client                                 Server
     |                                      |
     |  1. User selects image and options   |
     |------------------------------------->|
     |  2. POST /ocr (multipart/form-data)  |
     |------------------------------------->|
     |  3. Server processes the image       |
     |  4. Server returns JSON response     |
     |<-------------------------------------|
     |  5. Client displays results          |
   ```

3. **Form Data Parameters**:

   - `file`: The image file to be processed
   - `ocr_type`: Either 'ocr' for basic text extraction or 'format' for formatted output
   - `use_crop`: 'true' or 'false' - whether to use multi-crop mode
   - `render`: 'true' or 'false' - whether to return HTML rendering
   - `ocr_box`: Optional parameter to specify regions for OCR

4. **JavaScript Implementation**:
   The client.html file implements this using the Fetch API:

   ```javascript
   const formData = new FormData();
   formData.append("file", file);
   formData.append("ocr_type", ocrType);
   formData.append("use_crop", useCrop);
   formData.append("render", renderOutput);

   const response = await fetch("http://localhost:5000/ocr", {
     method: "POST",
     body: formData,
   });
   ```

### How the Backend Handles Requests

1. **Request Processing Flow**:

   ```
   ┌─────────────────┐     ┌────────────────┐     ┌───────────────────┐
   │ Receive Request │────>│ Validate Input │────>│ Save Uploaded File│
   └─────────────────┘     └────────────────┘     └───────────────────┘
            │                                               │
            │                                               ▼
   ┌─────────────────┐     ┌────────────────┐     ┌───────────────────┐
   │ Return Response │<────│ Format Results │<────│ Process with Model│
   └─────────────────┘     └────────────────┘     └───────────────────┘
   ```

2. **Backend Implementation**:
   The server code in ocr_processor.py:

   ```python
   @app.route('/ocr', methods=['POST'])
   def process_ocr():
       # 1. Validate the uploaded file
       if 'file' not in request.files:
           return jsonify({"error": "No file part"}), 400

       file = request.files['file']
       if file.filename == '' or not allowed_file(file.filename):
           return jsonify({"error": "Invalid file"}), 400

       # 2. Get the OCR parameters
       ocr_type = request.form.get('ocr_type', 'ocr')
       use_crop = request.form.get('use_crop', 'false').lower() == 'true'
       render = request.form.get('render', 'false').lower() == 'true'

       # 3. Save the uploaded file
       filename = secure_filename(file.filename)
       filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
       file.save(filepath)

       # 4. Process with the model
       if use_crop:
           res = model.chat_crop(tokenizer, filepath, ocr_type=ocr_type)
       else:
           if render:
               # Generate HTML rendering
               render_file = os.path.join(app.config['UPLOAD_FOLDER'], 'render.html')
               res = model.chat(tokenizer, filepath, ocr_type=ocr_type,
                               render=True, save_render_file=render_file)
               # Return both text and HTML
               with open(render_file, 'r') as f:
                   html_content = f.read()
               return jsonify({"text": res, "html": html_content})
           else:
               res = model.chat(tokenizer, filepath, ocr_type=ocr_type)

       # 5. Return the results
       return jsonify({"text": res})
   ```

3. **Security Measures**:
   - File validation to prevent uploads of unsupported formats
   - Use of `secure_filename()` to sanitize filenames
   - Temporary file storage with cleanup
   - Request size limitations (16MB limit)

## API Reference

### GET /health

Check the server health and status.

**Response**:

```json
{
  "status": "ok",
  "model_loaded": true
}
```

### POST /ocr

Process an image and extract text.

**Parameters**:

- `file`: The image file to process (required)
- `ocr_type`: OCR processing type - "ocr" (basic) or "format" (preserve formatting) (default: "ocr")
- `use_crop`: Whether to use multi-crop mode - "true" or "false" (default: "false")
- `render`: Whether to generate HTML rendering - "true" or "false" (default: "false")
- `ocr_box`: Specify regions to OCR (optional format: "x1,y1,x2,y2,x3,y3,x4,y4")

**Response**:

Without rendering:

```json
{
  "text": "Extracted text content..."
}
```

With rendering:

```json
{
  "text": "Extracted text content...",
  "html": "<!DOCTYPE html>...(HTML content)..."
}
```

## Rendering OCR Results

The OCR service provides two main ways to handle rendered HTML content from formatted OCR results.

### 1. Direct Rendering in the Response

When the `render=true` parameter is sent, the OCR API will return HTML content directly in the response:

```python
# Backend code (ocr_processor.py)
if render:
    render_file = os.path.join(app.config['UPLOAD_FOLDER'], 'render.html')
    res = model.chat(tokenizer, filepath, ocr_type=ocr_type,
                    render=True, save_render_file=render_file)
    with open(render_file, 'r') as f:
        html_content = f.read()
    return jsonify({"text": res, "html": html_content})
```

The client can then inject this HTML into the page:

```javascript
// Client code (client.html)
if (data.html) {
  renderedContent.style.display = "block";
  htmlOutput.innerHTML = data.html;
}
```

### 2. Saving to demo.html File

When you run OCR processing with rendering outside the API:

```python
# Direct model usage
res = model.chat(tokenizer, image_file, ocr_type='format', render=True, save_render_file='./demo.html')
```

The model will save the rendered HTML to the specified file (`demo.html`). You can then navigate to this file directly in your browser to view the formatted output.

### Accessing the Rendered HTML File

To open the rendered HTML file:

1. **From the API**: The HTML content is returned in the response and displayed in the client interface.

2. **From direct model usage**: After running the above code, open the demo.html file in your browser:
   - For local development: Navigate to `file:///path/to/your/project/demo.html`
   - If served through a web server: Navigate to `http://your-server/demo.html`

Example of direct rendering:

```python
# Example script to generate demo.html
from transformers import AutoModel, AutoTokenizer

# Load model and tokenizer
tokenizer = AutoTokenizer.from_pretrained("./model/snapshots/979938bf89ccdc949c0131ddd3841e24578a4742", trust_remote_code=True)
model = AutoModel.from_pretrained("./model/snapshots/979938bf89ccdc949c0131ddd3841e24578a4742", trust_remote_code=True).eval()

# Process image with rendering
image_path = "test.jpg"
result = model.chat(tokenizer, image_path, ocr_type='format', render=True, save_render_file='./demo.html')

print(f"OCR complete. Open demo.html in your browser to view formatted results.")
```

## Technical Details

### Model Information

This project uses a fine-tuned Qwen Vision model specifically adapted for OCR tasks. The model is capable of:

- Text detection and recognition
- Table structure recognition
- Mathematical notation parsing
- Multiple languages support
- Handling various image formats and qualities

### Backend Implementation

The backend is implemented using:

- **Flask**: For the web API framework
- **Transformers**: For loading and running the Hugging Face model
- **PyTorch**: As the underlying deep learning framework
- **OpenCV**: For image processing

The server loads the model at startup and keeps it in memory for fast inference. Temporary files are managed securely, and all API endpoints include proper error handling.

### Performance Considerations

- **GPU Acceleration**: The system automatically uses CUDA if available
- **Memory Usage**: The model requires approximately 4-8GB of VRAM or RAM
- **Processing Time**: Typical processing time is 1-5 seconds per image on GPU

## Troubleshooting

### Common Issues

1. **Rendering Problems**:

   - **Issue**: HTML rendering doesn't appear in browser
   - **Solution**: Ensure the path to demo.html is correct and accessible from your browser
   - **Fix**: If using the API, check that the `render` parameter is set to `true`

2. **Model Loading Errors**:

   - Ensure all model files are properly downloaded
   - Check CUDA compatibility if using GPU

3. **Memory Errors**:

   - Try processing smaller images
   - Use CPU mode if GPU memory is insufficient

4. **API Connection Issues**:

   - Verify the server is running
   - Check network settings and firewall rules

5. **Image Processing Errors**:
   - Ensure image is readable and in a supported format
   - Try different OCR options

### Getting Help

If you encounter issues not covered in this documentation, please:

1. Check the server logs for detailed error messages
2. Try running the API test script with various options
3. Report issues with full error details and steps to reproduce
