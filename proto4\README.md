# Basic OCR Backend Service

This project is a simplified Optical Character Recognition (OCR) service built around a Qwen Vision model, providing basic text extraction from images. The system is designed as a REST API for easy integration with frontend applications.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Setup and Installation](#setup-and-installation)
- [API Reference](#api-reference)
- [Technical Details](#technical-details)
- [Troubleshooting](#troubleshooting)

## Overview

This OCR service uses a pre-trained Qwen Vision model to extract plain text from images. The backend is built with Flask and provides a simple RESTful API that accepts image uploads and returns the extracted text.

## Features

- **Basic OCR**: Extracts plain text from images
- **RESTful API**: Simple HTTP interface for integration with any client application
- **Cross-Origin Support**: Built-in CORS support for web integrations
- **Simple Interface**: Clean, minimal API with no complex configuration

## Setup and Installation

### Prerequisites

- Python 3.8+ (3.10 recommended)
- CUDA-compatible GPU (optional but recommended for performance)

### Installation Steps

1. **Navigate to the proto4 folder**:

   ```bash
   cd proto4
   ```

2. **Activate the virtual environment**:

   - Windows (PowerShell):
     ```bash
     .\ocr_env\Scripts\Activate.ps1
     ```
   - Windows (Command Prompt):
     ```bash
     ocr_env\Scripts\activate.bat
     ```

3. **Install dependencies** (if not already installed):

   ```bash
   pip install -r requirements.txt
   ```

4. **Start the server**:
   ```bash
   python ocr_processor.py
   ```
   The server will start on `http://localhost:5000`

## Usage

### Web Interface

1. Open `client.html` in your browser
2. Select an image file
3. Click "Process Image"
4. View the extracted text

### API Usage

The OCR service uses a simple HTTP multipart/form-data request:

```javascript
const formData = new FormData();
formData.append("file", imageFile);

const response = await fetch("http://localhost:5000/ocr", {
  method: "POST",
  body: formData,
});

const data = await response.json();
console.log(data.text); // Extracted text
```

## API Reference

### GET /health

Check the server health and status.

**Response**:

```json
{
  "status": "ok",
  "model_loaded": true
}
```

### POST /ocr

Process an image and extract text.

**Parameters**:

- `file`: The image file to process (required)

**Response**:

```json
{
  "text": "Extracted text content..."
}
```

## Technical Details

### Model Information

This project uses a fine-tuned Qwen Vision model specifically adapted for basic OCR tasks. The model provides:

- Text detection and recognition
- Support for various image formats
- Multiple languages support

### Backend Implementation

The backend is implemented using:

- **Flask**: For the web API framework
- **Transformers**: For loading and running the Hugging Face model
- **PyTorch**: As the underlying deep learning framework

The server loads the model at startup and keeps it in memory for fast inference. Temporary files are managed securely.

### Performance Considerations

- **GPU Acceleration**: The system automatically uses CUDA if available
- **Memory Usage**: The model requires approximately 4-8GB of VRAM or RAM
- **Processing Time**: Typical processing time is 1-5 seconds per image on GPU

## Troubleshooting

### Common Issues

1. **Model Loading Errors**:

   - Ensure all model files are properly downloaded
   - Check CUDA compatibility if using GPU

2. **Memory Errors**:

   - Try processing smaller images
   - Use CPU mode if GPU memory is insufficient

3. **API Connection Issues**:

   - Verify the server is running on `http://localhost:5000`
   - Check network settings and firewall rules

4. **Image Processing Errors**:
   - Ensure image is readable and in a supported format (PNG, JPG, JPEG, GIF, TIF, TIFF)
   - Check file size (max 16MB)
